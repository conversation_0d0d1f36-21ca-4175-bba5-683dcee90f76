# DENTAL LAB DDD - Domain Events
# Events that occur in the business domain

from datetime import datetime
from dataclasses import dataclass
from typing import Optional, Dict, Any
from decimal import Decimal


@dataclass(frozen=True)
class DomainEvent:
    """Base class for all domain events"""
    occurred_at: datetime
    
    def __post_init__(self):
        if self.occurred_at is None:
            object.__setattr__(self, 'occurred_at', datetime.now())


# Case Events
@dataclass(frozen=True)
class CaseCreated(DomainEvent):
    """Event fired when a new case is created"""
    case_number: int
    dentist_id: int
    patient_id: Optional[int] = None
    created_by: Optional[int] = None


@dataclass(frozen=True)
class CaseStarted(DomainEvent):
    """Event fired when a case moves from pending to in-progress"""
    case_number: int
    started_at: datetime
    started_by: Optional[int] = None


@dataclass(frozen=True)
class CaseCompleted(DomainEvent):
    """Event fired when a case is completed"""
    case_number: int
    completed_at: datetime
    total_duration: Optional[str] = None  # ISO duration string
    completed_by: Optional[int] = None


@dataclass(frozen=True)
class CaseShipped(DomainEvent):
    """Event fired when a case is shipped"""
    case_number: int
    shipped_at: datetime
    tracking_number: Optional[str] = None
    shipped_by: Optional[int] = None


@dataclass(frozen=True)
class CaseDelivered(DomainEvent):
    """Event fired when a case is delivered"""
    case_number: int
    delivered_at: datetime
    delivery_confirmation: Optional[str] = None


@dataclass(frozen=True)
class CaseCancelled(DomainEvent):
    """Event fired when a case is cancelled"""
    case_number: int
    cancelled_at: datetime
    reason: str
    cancelled_by: Optional[int] = None


@dataclass(frozen=True)
class CaseRevisionRequired(DomainEvent):
    """Event fired when a case requires revision"""
    case_number: int
    revision_number: int
    reason: str
    requested_by: Optional[int] = None
    quality_issues: Optional[Dict[str, Any]] = None


@dataclass(frozen=True)
class CaseOnHold(DomainEvent):
    """Event fired when a case is put on hold"""
    case_number: int
    reason: str
    estimated_resume_date: Optional[datetime] = None
    put_on_hold_by: Optional[int] = None


@dataclass(frozen=True)
class CaseDeadlineChanged(DomainEvent):
    """Event fired when case deadline is modified"""
    case_number: int
    old_deadline: Optional[datetime]
    new_deadline: datetime
    reason: Optional[str] = None
    changed_by: Optional[int] = None


# Item Events
@dataclass(frozen=True)
class ItemAddedToCase(DomainEvent):
    """Event fired when an item is added to a case"""
    case_number: int
    item_id: int
    quantity: int
    unit_id: int
    estimated_time: Optional[str] = None  # ISO duration string
    added_by: Optional[int] = None


@dataclass(frozen=True)
class ItemRemovedFromCase(DomainEvent):
    """Event fired when an item is removed from a case"""
    case_number: int
    item_id: int
    reason: Optional[str] = None
    removed_by: Optional[int] = None


@dataclass(frozen=True)
class ItemProductionStarted(DomainEvent):
    """Event fired when production of an item starts"""
    case_number: int
    item_id: int
    started_at: datetime
    assigned_technician: Optional[int] = None


@dataclass(frozen=True)
class ItemProductionCompleted(DomainEvent):
    """Event fired when production of an item is completed"""
    case_number: int
    item_id: int
    completed_at: datetime
    actual_time: str  # ISO duration string
    quality_passed: bool
    completed_by: Optional[int] = None


# Task Events
@dataclass(frozen=True)
class TaskCreated(DomainEvent):
    """Event fired when a new task is created"""
    task_id: int
    case_number: int
    title: str
    estimated_duration: str  # ISO duration string
    workflow_stage_id: Optional[int] = None
    created_by: Optional[int] = None


@dataclass(frozen=True)
class TaskAssigned(DomainEvent):
    """Event fired when a task is assigned to someone"""
    task_id: int
    case_number: int
    assigned_to: int
    assigned_by: Optional[int] = None
    assignment_reason: Optional[str] = None


@dataclass(frozen=True)
class TaskStarted(DomainEvent):
    """Event fired when a task is started"""
    task_id: int
    case_number: int
    started_at: datetime
    started_by: int


@dataclass(frozen=True)
class TaskCompleted(DomainEvent):
    """Event fired when a task is completed"""
    task_id: int
    case_number: int
    completed_at: datetime
    actual_duration: str  # ISO duration string
    quality_check_passed: Optional[bool] = None
    completed_by: Optional[int] = None


@dataclass(frozen=True)
class TaskPaused(DomainEvent):
    """Event fired when a task is paused"""
    task_id: int
    case_number: int
    paused_at: datetime
    reason: str
    paused_by: Optional[int] = None


@dataclass(frozen=True)
class TaskBlocked(DomainEvent):
    """Event fired when a task is blocked"""
    task_id: int
    case_number: int
    blocked_at: datetime
    blocking_reason: str
    blocked_by: Optional[int] = None


@dataclass(frozen=True)
class TaskOverdue(DomainEvent):
    """Event fired when a task becomes overdue"""
    task_id: int
    case_number: int
    original_deadline: datetime
    days_overdue: int
    assigned_to: Optional[int] = None


# Patient Events
@dataclass(frozen=True)
class PatientCreated(DomainEvent):
    """Event fired when a new patient is registered"""
    patient_id: int
    dentist_id: int
    patient_name: str
    created_by: Optional[int] = None


@dataclass(frozen=True)
class PatientContactUpdated(DomainEvent):
    """Event fired when patient contact info is updated"""
    patient_id: int
    updated_fields: Dict[str, str]  # field_name -> new_value
    updated_by: Optional[int] = None


# Dentist Events
@dataclass(frozen=True)
class DentistRegistered(DomainEvent):
    """Event fired when a new dentist is registered"""
    dentist_id: int
    dentist_name: str
    clinic_name: str
    registered_by: Optional[int] = None


# Quality Events
@dataclass(frozen=True)
class QualityCheckStarted(DomainEvent):
    """Event fired when quality check begins"""
    case_number: int
    quality_checker: int
    checklist_items: Dict[str, Any]


@dataclass(frozen=True)
class QualityCheckPassed(DomainEvent):
    """Event fired when quality check passes"""
    case_number: int
    quality_checker: int
    check_results: Dict[str, Any]
    notes: Optional[str] = None


@dataclass(frozen=True)
class QualityCheckFailed(DomainEvent):
    """Event fired when quality check fails"""
    case_number: int
    quality_checker: int
    failed_items: Dict[str, str]  # item -> reason
    required_actions: Dict[str, str]


# Workflow Events
@dataclass(frozen=True)
class WorkflowStageChanged(DomainEvent):
    """Event fired when case moves to a different workflow stage"""
    case_number: int
    from_stage_id: Optional[int]
    to_stage_id: int
    stage_duration: Optional[str] = None  # ISO duration string
    changed_by: Optional[int] = None


# Notification Events
@dataclass(frozen=True)
class DeadlineApproaching(DomainEvent):
    """Event fired when case deadline is approaching"""
    case_number: int
    deadline: datetime
    days_remaining: int
    dentist_id: int
    assigned_technicians: list[int]


@dataclass(frozen=True)
class CaseOverdue(DomainEvent):
    """Event fired when a case becomes overdue"""
    case_number: int
    deadline: datetime
    days_overdue: int
    dentist_id: int
    responsible_department: Optional[int] = None


# Integration Events (for external systems)
@dataclass(frozen=True)
class InventoryUpdateRequired(DomainEvent):
    """Event fired when inventory needs to be updated due to case items"""
    case_number: int
    items_consumed: Dict[int, int]  # item_id -> quantity
    update_type: str  # 'consume' or 'return'


@dataclass(frozen=True)
class BillingEventOccurred(DomainEvent):
    """Event fired when something happens that affects billing"""
    case_number: int
    event_type: str  # 'case_completed', 'revision_added', etc.
    billing_amount: Optional[Decimal] = None
    currency: str = "EUR"