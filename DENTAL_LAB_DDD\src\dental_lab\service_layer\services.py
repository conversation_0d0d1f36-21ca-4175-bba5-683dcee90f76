# DENTAL LAB DDD - Service Layer (Use Cases)
# Business use case implementations that coordinate domain objects

from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from decimal import Decimal

from dental_lab.domain import model, exceptions, events, commands
from dental_lab.service_layer import uow


# Case Management Services
def create_new_case(
    dentist_id: int,
    patient_id: Optional[int] = None,
    items: Optional[List[Dict[str, Any]]] = None,
    deadline: Optional[datetime] = None,
    priority: int = 2,
    notes: str = "",
    special_requirements: str = "",
    workflow_template_id: Optional[int] = None,
    uow: uow.AbstractUnitOfWork = None
) -> int:
    """
    Use case: Create a new dental case
    
    Args:
        dentist_id: ID of the dentist creating the case
        patient_id: Optional patient ID
        items: List of items to add to the case
        deadline: Case deadline
        priority: Priority level (1=Low, 2=Normal, 3=High, 4=Urgent)
        notes: Additional notes
        special_requirements: Special requirements
        workflow_template_id: Workflow template to use
        uow: Unit of Work instance
    
    Returns:
        case_number: The number of the created case
    
    Raises:
        DentistNotFound: If dentist doesn't exist
        PatientNotFound: If patient doesn't exist
        PatientDentistMismatch: If patient doesn't belong to dentist
    """
    with uow:
        # Verify dentist exists
        dentist = uow.dentists.get_by_dentist_id(dentist_id)
        if not dentist:
            raise exceptions.DentistNotFound(dentist_id)
        
        # Verify patient if provided
        if patient_id:
            patient = uow.patients.get_by_patient_id(patient_id)
            if not patient:
                raise exceptions.PatientNotFound(patient_id)
            
            # Verify patient belongs to dentist
            if patient.dentist_id != dentist_id:
                raise exceptions.PatientDentistMismatch(
                    patient_id, dentist_id, patient.dentist_id
                )
        
        # Generate next case number
        next_case_number = uow.cases.get_next_case_number()
        
        # Create the case
        case = model.Case(
            case_number=next_case_number,
            dentist_id=dentist_id,
            patient_id=patient_id,
            deadline=deadline
        )
        
        # Set additional properties
        case.priority = model.Priority(priority)
        case.notes = notes
        case.special_requirements = special_requirements
        case.workflow_template_id = workflow_template_id
        
        # Add items if provided
        if items:
            for item_data in items:
                case.add_item(
                    item_id=item_data['item_id'],
                    quantity=item_data['quantity'],
                    unit_id=item_data['unit_id'],
                    estimated_time=item_data.get('estimated_time')
                )
        
        # Save the case
        uow.cases.add(case)
        uow.track_case(case)  # Track for events
        uow.commit()
        
        return case.case_number


def get_case_details(case_number: int, uow: uow.AbstractUnitOfWork) -> model.Case:
    """
    Use case: Get detailed information about a case
    
    Args:
        case_number: The case number to retrieve
        uow: Unit of Work instance
    
    Returns:
        Case: The case with all details loaded
    
    Raises:
        CaseNotFound: If case doesn't exist
    """
    with uow:
        case = uow.cases.get_by_case_number(case_number)
        if not case:
            raise exceptions.CaseNotFound(case_number)
        
        return case


def start_case(
    case_number: int,
    started_by: Optional[int] = None,
    uow: uow.AbstractUnitOfWork = None
) -> None:
    """
    Use case: Start a case (move from pending to in progress)
    
    Args:
        case_number: Case to start
        started_by: User ID who started the case
        uow: Unit of Work instance
    
    Raises:
        CaseNotFound: If case doesn't exist
        InvalidCaseStatus: If case cannot be started
        CaseHasNoItems: If case has no items
    """
    with uow:
        case = uow.cases.get_by_case_number(case_number)
        if not case:
            raise exceptions.CaseNotFound(case_number)
        
        # Start the case (domain logic will validate)
        case.start_case()
        
        # Update in repository
        uow.cases.update(case)
        uow.track_case(case)
        uow.commit()


def complete_case(
    case_number: int,
    completed_by: Optional[int] = None,
    completion_notes: str = "",
    uow: uow.AbstractUnitOfWork = None
) -> None:
    """
    Use case: Complete a case
    
    Args:
        case_number: Case to complete
        completed_by: User ID who completed the case
        completion_notes: Notes about completion
        uow: Unit of Work instance
    
    Raises:
        CaseNotFound: If case doesn't exist
        InvalidCaseStatus: If case cannot be completed
        CaseItemsNotCompleted: If case items are not all completed
    """
    with uow:
        case = uow.cases.get_by_case_number(case_number)
        if not case:
            raise exceptions.CaseNotFound(case_number)
        
        # Complete the case (domain logic will validate)
        case.complete_case()
        
        if completion_notes:
            case.notes += f"\nCompleted: {completion_notes}"
        
        # Update in repository
        uow.cases.update(case)
        uow.track_case(case)
        uow.commit()


def add_item_to_case(
    case_number: int,
    item_id: int,
    quantity: int,
    unit_id: int,
    estimated_time: Optional[timedelta] = None,
    special_instructions: str = "",
    added_by: Optional[int] = None,
    uow: uow.AbstractUnitOfWork = None
) -> None:
    """
    Use case: Add an item to an existing case
    
    Args:
        case_number: Case to add item to
        item_id: ID of the item to add
        quantity: Quantity of the item
        unit_id: Unit of measurement
        estimated_time: Estimated production time
        special_instructions: Special instructions for this item
        added_by: User ID who added the item
        uow: Unit of Work instance
    
    Raises:
        CaseNotFound: If case doesn't exist
        InvalidQuantity: If quantity is invalid
        InvalidCaseStatus: If case cannot be modified
    """
    with uow:
        case = uow.cases.get_by_case_number(case_number)
        if not case:
            raise exceptions.CaseNotFound(case_number)
        
        if quantity <= 0:
            raise exceptions.InvalidQuantity(quantity, "Quantity must be positive")
        
        # Add item to case (domain logic will validate)
        case.add_item(item_id, quantity, unit_id, estimated_time)
        
        # Add special instructions to the case item
        if special_instructions:
            case_item = next(item for item in case.items if item.item_id == item_id)
            case_item.notes = special_instructions
        
        # Update in repository
        uow.cases.update(case)
        uow.track_case(case)
        uow.commit()


def update_case_deadline(
    case_number: int,
    new_deadline: datetime,
    reason: str = "",
    updated_by: Optional[int] = None,
    notify_dentist: bool = True,
    uow: uow.AbstractUnitOfWork = None
) -> None:
    """
    Use case: Update case deadline
    
    Args:
        case_number: Case to update
        new_deadline: New deadline
        reason: Reason for deadline change
        updated_by: User ID who updated the deadline
        notify_dentist: Whether to notify the dentist
        uow: Unit of Work instance
    
    Raises:
        CaseNotFound: If case doesn't exist
        InvalidDeadline: If deadline is invalid
    """
    with uow:
        case = uow.cases.get_by_case_number(case_number)
        if not case:
            raise exceptions.CaseNotFound(case_number)
        
        old_deadline = case.deadline
        
        # Set new deadline (domain logic will validate)
        case.set_deadline(new_deadline)
        
        if reason:
            case.notes += f"\nDeadline changed: {reason}"
        
        # Update in repository
        uow.cases.update(case)
        uow.track_case(case)
        uow.commit()


def cancel_case(
    case_number: int,
    reason: str,
    cancelled_by: Optional[int] = None,
    refund_amount: Optional[Decimal] = None,
    notify_dentist: bool = True,
    uow: uow.AbstractUnitOfWork = None
) -> None:
    """
    Use case: Cancel a case
    
    Args:
        case_number: Case to cancel
        reason: Reason for cancellation
        cancelled_by: User ID who cancelled the case
        refund_amount: Refund amount if applicable
        notify_dentist: Whether to notify the dentist
        uow: Unit of Work instance
    
    Raises:
        CaseNotFound: If case doesn't exist
        InvalidCaseStatus: If case cannot be cancelled
    """
    with uow:
        case = uow.cases.get_by_case_number(case_number)
        if not case:
            raise exceptions.CaseNotFound(case_number)
        
        # Cancel the case (domain logic will validate)
        case.cancel_case(reason)
        
        # Update in repository
        uow.cases.update(case)
        uow.track_case(case)
        uow.commit()


def list_overdue_cases(uow: uow.AbstractUnitOfWork) -> List[model.Case]:
    """
    Use case: List all overdue cases
    
    Args:
        uow: Unit of Work instance
    
    Returns:
        List of overdue cases
    """
    with uow:
        return uow.cases.list_overdue_cases()


def list_cases_by_dentist(
    dentist_id: int, 
    include_completed: bool = False,
    uow: uow.AbstractUnitOfWork = None
) -> List[model.Case]:
    """
    Use case: List cases for a specific dentist
    
    Args:
        dentist_id: Dentist ID to filter by
        include_completed: Whether to include completed cases
        uow: Unit of Work instance
    
    Returns:
        List of cases for the dentist
    
    Raises:
        DentistNotFound: If dentist doesn't exist
    """
    with uow:
        # Verify dentist exists
        dentist = uow.dentists.get_by_dentist_id(dentist_id)
        if not dentist:
            raise exceptions.DentistNotFound(dentist_id)
        
        cases = uow.cases.list_cases_by_dentist(dentist_id)
        
        if not include_completed:
            cases = [
                case for case in cases
                if case.status not in [model.CaseStatus.COMPLETED, model.CaseStatus.CANCELLED]
            ]
        
        return cases


def get_cases_due_soon(
    days: int = 3,
    uow: uow.AbstractUnitOfWork = None
) -> List[model.Case]:
    """
    Use case: Get cases due within specified number of days
    
    Args:
        days: Number of days to look ahead
        uow: Unit of Work instance
    
    Returns:
        List of cases due soon
    """
    with uow:
        return uow.cases.list_cases_due_soon(days)


# Patient Management Services
def register_patient(
    dentist_id: int,
    first_name: str,
    last_name: str,
    phone_number: Optional[str] = None,
    email: Optional[str] = None,
    address: Optional[str] = None,
    date_of_birth: Optional[datetime] = None,
    registered_by: Optional[int] = None,
    uow: uow.AbstractUnitOfWork = None
) -> int:
    """
    Use case: Register a new patient
    
    Args:
        dentist_id: ID of the dentist
        first_name: Patient's first name
        last_name: Patient's last name
        phone_number: Patient's phone number
        email: Patient's email
        address: Patient's address
        date_of_birth: Patient's date of birth
        registered_by: User ID who registered the patient
        uow: Unit of Work instance
    
    Returns:
        patient_id: The ID of the created patient
    
    Raises:
        DentistNotFound: If dentist doesn't exist
        InvalidPatientData: If patient data is invalid
    """
    with uow:
        # Verify dentist exists
        dentist = uow.dentists.get_by_dentist_id(dentist_id)
        if not dentist:
            raise exceptions.DentistNotFound(dentist_id)
        
        # Validate patient data
        if not first_name.strip():
            raise exceptions.InvalidPatientData("first_name", first_name, "First name cannot be empty")
        
        # Generate patient ID
        # In a real implementation, this might be auto-generated by the database
        existing_patients = uow.patients.list_patients_by_dentist(dentist_id)
        next_patient_id = max([p.patient_id for p in existing_patients], default=0) + 1
        
        # Create patient
        name = model.PersonName(first_name, last_name)
        contact = model.ContactInfo(phone=phone_number, email=email, address=address)
        
        patient = model.Patient(
            patient_id=next_patient_id,
            name=name,
            dentist_id=dentist_id,
            contact=contact,
            date_of_birth=date_of_birth
        )
        
        # Save patient
        uow.patients.add(patient)
        uow.commit()
        
        return patient.patient_id


def get_patient_details(
    patient_id: int,
    uow: uow.AbstractUnitOfWork = None
) -> model.Patient:
    """
    Use case: Get patient details
    
    Args:
        patient_id: Patient ID to retrieve
        uow: Unit of Work instance
    
    Returns:
        Patient: The patient details
    
    Raises:
        PatientNotFound: If patient doesn't exist
    """
    with uow:
        patient = uow.patients.get_by_patient_id(patient_id)
        if not patient:
            raise exceptions.PatientNotFound(patient_id)
        
        return patient


def search_patients(
    name_query: str,
    dentist_id: Optional[int] = None,
    uow: uow.AbstractUnitOfWork = None
) -> List[model.Patient]:
    """
    Use case: Search patients by name
    
    Args:
        name_query: Name to search for
        dentist_id: Optional dentist ID to filter by
        uow: Unit of Work instance
    
    Returns:
        List of matching patients
    """
    with uow:
        return uow.patients.search_patients(name_query, dentist_id)


# Dentist Management Services
def register_dentist(
    first_name: str,
    last_name: str,
    clinic_name: str,
    phone_number: str,
    email: Optional[str] = None,
    address: Optional[str] = None,
    user_id: Optional[int] = None,
    registered_by: Optional[int] = None,
    uow: uow.AbstractUnitOfWork = None
) -> int:
    """
    Use case: Register a new dentist
    
    Args:
        first_name: Dentist's first name
        last_name: Dentist's last name
        clinic_name: Name of the clinic
        phone_number: Phone number
        email: Email address
        address: Address
        user_id: Associated user account ID
        registered_by: User ID who registered the dentist
        uow: Unit of Work instance
    
    Returns:
        dentist_id: The ID of the created dentist
    
    Raises:
        InvalidDentistData: If dentist data is invalid
    """
    with uow:
        # Validate data
        if not first_name.strip():
            raise exceptions.InvalidDentistData("first_name", first_name, "First name cannot be empty")
        
        if not clinic_name.strip():
            raise exceptions.InvalidDentistData("clinic_name", clinic_name, "Clinic name cannot be empty")
        
        # Generate dentist ID
        existing_dentists = uow.dentists.list_all_dentists()
        next_dentist_id = max([d.dentist_id for d in existing_dentists], default=99) + 1
        
        # Create dentist
        name = model.PersonName(first_name, last_name)
        contact = model.ContactInfo(phone=phone_number, email=email, address=address)
        
        dentist = model.Dentist(
            dentist_id=next_dentist_id,
            name=name,
            clinic_name=clinic_name,
            contact=contact,
            user_id=user_id
        )
        
        # Save dentist
        uow.dentists.add(dentist)
        uow.commit()
        
        return dentist.dentist_id


def get_dentist_details(
    dentist_id: int,
    uow: uow.AbstractUnitOfWork = None
) -> model.Dentist:
    """
    Use case: Get dentist details
    
    Args:
        dentist_id: Dentist ID to retrieve
        uow: Unit of Work instance
    
    Returns:
        Dentist: The dentist details
    
    Raises:
        DentistNotFound: If dentist doesn't exist
    """
    with uow:
        dentist = uow.dentists.get_by_dentist_id(dentist_id)
        if not dentist:
            raise exceptions.DentistNotFound(dentist_id)
        
        return dentist


def get_dentist_by_user(
    user_id: int,
    uow: uow.AbstractUnitOfWork = None
) -> model.Dentist:
    """
    Use case: Get dentist by user account ID
    
    Args:
        user_id: User account ID
        uow: Unit of Work instance
    
    Returns:
        Dentist: The dentist details
    
    Raises:
        DentistNotFound: If dentist doesn't exist
    """
    with uow:
        dentist = uow.dentists.get_by_user_id(user_id)
        if not dentist:
            raise exceptions.DentistNotFound(f"No dentist found for user {user_id}")
        
        return dentist


# Task Management Services
def create_task(
    case_number: int,
    title: str,
    description: str,
    estimated_duration: timedelta,
    priority: int = 2,
    workflow_stage_id: Optional[int] = None,
    case_item_id: Optional[int] = None,
    dependencies: Optional[List[int]] = None,
    created_by: Optional[int] = None,
    uow: uow.AbstractUnitOfWork = None
) -> int:
    """
    Use case: Create a new task
    
    Args:
        case_number: Case the task belongs to
        title: Task title
        description: Task description
        estimated_duration: Estimated time to complete
        priority: Priority level
        workflow_stage_id: Workflow stage this task belongs to
        case_item_id: Case item this task is for
        dependencies: List of task IDs this task depends on
        created_by: User ID who created the task
        uow: Unit of Work instance
    
    Returns:
        task_id: The ID of the created task
    
    Raises:
        CaseNotFound: If case doesn't exist
    """
    with uow:
        # Verify case exists
        case = uow.cases.get_by_case_number(case_number)
        if not case:
            raise exceptions.CaseNotFound(case_number)
        
        # Generate task ID
        next_task_id = uow.tasks.get_next_task_id()
        
        # Create task
        task = model.Task(
            task_id=next_task_id,
            case_number=case_number,
            title=title,
            estimated_duration=estimated_duration,
            case_item_id=case_item_id,
            workflow_stage_id=workflow_stage_id
        )
        
        # Set additional properties
        task.priority = model.Priority(priority)
        if dependencies:
            task.dependencies = set(dependencies)
        
        # Add task to case and save
        case.add_task(task)
        uow.tasks.add(task)
        uow.cases.update(case)
        uow.track_case(case)
        uow.commit()
        
        return task.task_id


def assign_task(
    task_id: int,
    assigned_to: int,
    assignment_notes: str = "",
    assigned_by: Optional[int] = None,
    uow: uow.AbstractUnitOfWork = None
) -> None:
    """
    Use case: Assign a task to someone
    
    Args:
        task_id: Task to assign
        assigned_to: User ID to assign task to
        assignment_notes: Notes about the assignment
        assigned_by: User ID who made the assignment
        uow: Unit of Work instance
    
    Raises:
        TaskNotFound: If task doesn't exist
    """
    with uow:
        task = uow.tasks.get_by_task_id(task_id)
        if not task:
            raise exceptions.TaskNotFound(task_id)
        
        task.assigned_to = assigned_to
        if assignment_notes:
            task.notes += f"\nAssigned: {assignment_notes}"
        
        # Update task
        uow.tasks.update(task)
        uow.commit()


def start_task(
    task_id: int,
    started_by: Optional[int] = None,
    start_notes: str = "",
    uow: uow.AbstractUnitOfWork = None
) -> None:
    """
    Use case: Start a task
    
    Args:
        task_id: Task to start
        started_by: User ID who started the task
        start_notes: Notes about starting the task
        uow: Unit of Work instance
    
    Raises:
        TaskNotFound: If task doesn't exist
        InvalidTaskStatus: If task cannot be started
        TaskDependenciesNotMet: If task dependencies are not completed
    """
    with uow:
        task = uow.tasks.get_by_task_id(task_id)
        if not task:
            raise exceptions.TaskNotFound(task_id)
        
        # Start the task (domain logic will validate)
        task.start_task()
        
        if start_notes:
            task.notes += f"\nStarted: {start_notes}"
        
        # Update task
        uow.tasks.update(task)
        uow.commit()


def complete_task(
    task_id: int,
    completed_by: Optional[int] = None,
    completion_notes: str = "",
    quality_check_passed: Optional[bool] = None,
    uow: uow.AbstractUnitOfWork = None
) -> None:
    """
    Use case: Complete a task
    
    Args:
        task_id: Task to complete
        completed_by: User ID who completed the task
        completion_notes: Notes about completion
        quality_check_passed: Whether quality check passed
        uow: Unit of Work instance
    
    Raises:
        TaskNotFound: If task doesn't exist
        InvalidTaskStatus: If task cannot be completed
    """
    with uow:
        task = uow.tasks.get_by_task_id(task_id)
        if not task:
            raise exceptions.TaskNotFound(task_id)
        
        # Set quality check result if provided
        if quality_check_passed is not None:
            task.quality_check_passed = quality_check_passed
        
        # Complete the task (domain logic will validate)
        task.complete_task()
        
        if completion_notes:
            task.review_comments = completion_notes
        
        # Update task
        uow.tasks.update(task)
        uow.commit()


def get_pending_tasks(
    assignee_id: Optional[int] = None,
    uow: uow.AbstractUnitOfWork = None
) -> List[model.Task]:
    """
    Use case: Get pending tasks
    
    Args:
        assignee_id: Optional filter by assignee
        uow: Unit of Work instance
    
    Returns:
        List of pending tasks
    """
    with uow:
        return uow.tasks.list_pending_tasks(assignee_id)


def get_overdue_tasks(uow: uow.AbstractUnitOfWork = None) -> List[model.Task]:
    """
    Use case: Get overdue tasks
    
    Args:
        uow: Unit of Work instance
    
    Returns:
        List of overdue tasks
    """
    with uow:
        return uow.tasks.list_overdue_tasks()


# Analytics and Reporting Services
def get_case_statistics(
    date_from: Optional[datetime] = None,
    date_to: Optional[datetime] = None,
    uow: uow.AbstractUnitOfWork = None
) -> Dict[str, Any]:
    """
    Use case: Get case statistics
    
    Args:
        date_from: Start date for statistics
        date_to: End date for statistics
        uow: Unit of Work instance
    
    Returns:
        Dictionary with case statistics
    """
    with uow:
        # Get status counts
        status_counts = uow.cases.count_cases_by_status()
        
        # Get overdue cases count
        overdue_cases = uow.cases.list_overdue_cases()
        overdue_count = len(overdue_cases)
        
        # Get cases due soon
        due_soon_cases = uow.cases.list_cases_due_soon()
        due_soon_count = len(due_soon_cases)
        
        return {
            'status_counts': status_counts,
            'overdue_count': overdue_count,
            'due_soon_count': due_soon_count,
            'total_active_cases': sum(
                count for status, count in status_counts.items()
                if status not in ['completed', 'cancelled']
            )
        }


# Workflow Management Services
def move_case_to_next_stage(
    case_number: int,
    stage_completion_notes: str = "",
    moved_by: Optional[int] = None,
    skip_validation: bool = False,
    uow: uow.AbstractUnitOfWork = None
) -> None:
    """
    Use case: Move case to next workflow stage
    
    Args:
        case_number: Case to move
        stage_completion_notes: Notes about stage completion
        moved_by: User ID who moved the case
        skip_validation: Whether to skip validation checks
        uow: Unit of Work instance
    
    Raises:
        CaseNotFound: If case doesn't exist
        InvalidWorkflowTransition: If stage transition is not allowed
    """
    with uow:
        case = uow.cases.get_by_case_number(case_number)
        if not case:
            raise exceptions.CaseNotFound(case_number)
        
        # Business rule: Cannot move completed or cancelled cases
        if case.status in [model.CaseStatus.COMPLETED, model.CaseStatus.CANCELLED]:
            raise exceptions.InvalidCaseStatus(
                case_number, case.status.value, "in_progress", "move to next stage"
            )
        
        # Business rule: All tasks in current stage must be completed
        if not skip_validation:
            current_stage_tasks = [
                task for task in case.tasks 
                if task.workflow_stage_id == case.current_stage_id
            ]
            incomplete_tasks = [
                task for task in current_stage_tasks 
                if task.status != model.TaskStatus.COMPLETED
            ]
            
            if incomplete_tasks:
                task_ids = [task.task_id for task in incomplete_tasks]
                raise exceptions.BusinessRuleViolation(
                    "stage_completion_required",
                    f"All tasks in current stage must be completed before moving to next stage",
                    {"incomplete_tasks": task_ids}
                )
        
        # Move to next stage (simplified - in real implementation would query workflow template)
        if case.current_stage_id:
            case.current_stage_id += 1
        else:
            case.current_stage_id = 1
        
        # Update case status based on stage
        if case.current_stage_id == 1:
            case.status = model.CaseStatus.IN_PROGRESS
        elif case.current_stage_id >= 5:  # Assuming 5 stages total
            case.status = model.CaseStatus.QUALITY_CHECK
        
        if stage_completion_notes:
            case.notes += f"\nStage {case.current_stage_id - 1} completed: {stage_completion_notes}"
        
        # Update case
        uow.cases.update(case)
        uow.track_case(case)
        uow.commit()


def assign_case_to_department(
    case_number: int,
    department_id: int,
    assigned_by: Optional[int] = None,
    assignment_reason: str = "",
    uow: uow.AbstractUnitOfWork = None
) -> None:
    """
    Use case: Assign case to a department
    
    Args:
        case_number: Case to assign
        department_id: Department to assign to
        assigned_by: User ID who made assignment
        assignment_reason: Reason for assignment
        uow: Unit of Work instance
    
    Raises:
        CaseNotFound: If case doesn't exist
    """
    with uow:
        case = uow.cases.get_by_case_number(case_number)
        if not case:
            raise exceptions.CaseNotFound(case_number)
        
        case.responsible_department_id = department_id
        
        if assignment_reason:
            case.notes += f"\nAssigned to department {department_id}: {assignment_reason}"
        
        # Update case
        uow.cases.update(case)
        uow.track_case(case)
        uow.commit()


# Quality Control Services
def start_quality_check(
    case_number: int,
    quality_checker: int,
    checklist_items: Dict[str, bool],
    notes: str = "",
    uow: uow.AbstractUnitOfWork = None
) -> None:
    """
    Use case: Start quality check for a case
    
    Args:
        case_number: Case to check
        quality_checker: User ID performing quality check
        checklist_items: Dictionary of checklist items and their status
        notes: Quality check notes
        uow: Unit of Work instance
    
    Raises:
        CaseNotFound: If case doesn't exist
        InvalidCaseStatus: If case is not ready for quality check
    """
    with uow:
        case = uow.cases.get_by_case_number(case_number)
        if not case:
            raise exceptions.CaseNotFound(case_number)
        
        # Business rule: Case must be ready for quality check
        if case.status not in [model.CaseStatus.READY_TO_SHIP, model.CaseStatus.IN_PROGRESS]:
            raise exceptions.InvalidCaseStatus(
                case_number, case.status.value, "ready_to_ship or in_progress", "start quality check"
            )
        
        # Set case status to quality check
        case.status = model.CaseStatus.QUALITY_CHECK
        
        # Store quality check data
        case.quality_checks = {
            'checker_id': quality_checker,
            'started_at': datetime.now().isoformat(),
            'checklist': checklist_items,
            'notes': notes,
            'status': 'in_progress'
        }
        
        # Update case
        uow.cases.update(case)
        uow.track_case(case)
        uow.commit()


def complete_quality_check(
    case_number: int,
    quality_checker: int,
    check_results: Dict[str, bool],
    overall_passed: bool,
    notes: str = "",
    required_actions: Optional[List[str]] = None,
    uow: uow.AbstractUnitOfWork = None
) -> None:
    """
    Use case: Complete quality check
    
    Args:
        case_number: Case that was checked
        quality_checker: User ID who performed check
        check_results: Results of quality check items
        overall_passed: Whether quality check passed overall
        notes: Quality check completion notes
        required_actions: List of required actions if failed
        uow: Unit of Work instance
    
    Raises:
        CaseNotFound: If case doesn't exist
        QualityCheckNotCompleted: If quality check wasn't started
    """
    with uow:
        case = uow.cases.get_by_case_number(case_number)
        if not case:
            raise exceptions.CaseNotFound(case_number)
        
        # Verify quality check was started
        if case.status != model.CaseStatus.QUALITY_CHECK:
            raise exceptions.QualityCheckNotCompleted(case_number)
        
        # Update quality check data
        case.quality_checks.update({
            'completed_at': datetime.now().isoformat(),
            'results': check_results,
            'passed': overall_passed,
            'completion_notes': notes,
            'status': 'completed'
        })
        
        if overall_passed:
            case.status = model.CaseStatus.READY_TO_SHIP
        else:
            case.status = model.CaseStatus.REVISION_NEEDED
            case.revision_count += 1
            
            if required_actions:
                case.notes += f"\nQuality check failed. Required actions: {', '.join(required_actions)}"
        
        # Update case
        uow.cases.update(case)
        uow.track_case(case)
        uow.commit()


def require_case_revision(
    case_number: int,
    reason: str,
    quality_issues: Dict[str, str],
    required_actions: List[str],
    requested_by: Optional[int] = None,
    notify_dentist: bool = True,
    uow: uow.AbstractUnitOfWork = None
) -> None:
    """
    Use case: Require revision for a case
    
    Args:
        case_number: Case requiring revision
        reason: Reason for revision
        quality_issues: Dictionary of quality issues
        required_actions: List of required corrective actions
        requested_by: User ID who requested revision
        notify_dentist: Whether to notify the dentist
        uow: Unit of Work instance
    
    Raises:
        CaseNotFound: If case doesn't exist
        InvalidCaseStatus: If case cannot be revised
    """
    with uow:
        case = uow.cases.get_by_case_number(case_number)
        if not case:
            raise exceptions.CaseNotFound(case_number)
        
        # Require revision (domain method will validate)
        case.require_revision(reason)
        
        # Store revision details
        revision_data = {
            'revision_number': case.revision_count,
            'requested_at': datetime.now().isoformat(),
            'requested_by': requested_by,
            'reason': reason,
            'quality_issues': quality_issues,
            'required_actions': required_actions
        }
        
        # Add to quality checks history
        if 'revisions' not in case.quality_checks:
            case.quality_checks['revisions'] = []
        case.quality_checks['revisions'].append(revision_data)
        
        # Update case
        uow.cases.update(case)
        uow.track_case(case)
        uow.commit()


# Production Management Services
def start_item_production(
    case_number: int,
    item_id: int,
    assigned_technician: Optional[int] = None,
    production_notes: str = "",
    started_by: Optional[int] = None,
    uow: uow.AbstractUnitOfWork = None
) -> None:
    """
    Use case: Start production of a case item
    
    Args:
        case_number: Case containing the item
        item_id: Item to start production for
        assigned_technician: Technician assigned to production
        production_notes: Notes about production start
        started_by: User ID who started production
        uow: Unit of Work instance
    
    Raises:
        CaseNotFound: If case doesn't exist
        ItemNotFound: If item doesn't exist in case
        InvalidCaseStatus: If case cannot start production
    """
    with uow:
        case = uow.cases.get_by_case_number(case_number)
        if not case:
            raise exceptions.CaseNotFound(case_number)
        
        # Find the case item
        case_item = next((item for item in case.items if item.item_id == item_id), None)
        if not case_item:
            raise exceptions.ItemNotFound(item_id)
        
        # Start production (domain method will validate)
        case_item.start_production()
        
        if production_notes:
            case_item.notes += f"\nProduction started: {production_notes}"
        
        # Update case
        uow.cases.update(case)
        uow.track_case(case)
        uow.commit()


def complete_item_production(
    case_number: int,
    item_id: int,
    actual_time: timedelta,
    quality_notes: str = "",
    quality_passed: bool = True,
    completed_by: Optional[int] = None,
    uow: uow.AbstractUnitOfWork = None
) -> None:
    """
    Use case: Complete production of a case item
    
    Args:
        case_number: Case containing the item
        item_id: Item to complete production for
        actual_time: Actual time taken for production
        quality_notes: Quality control notes
        quality_passed: Whether quality check passed
        completed_by: User ID who completed production
        uow: Unit of Work instance
    
    Raises:
        CaseNotFound: If case doesn't exist
        ItemNotFound: If item doesn't exist in case
        InvalidTaskStatus: If item production cannot be completed
    """
    with uow:
        case = uow.cases.get_by_case_number(case_number)
        if not case:
            raise exceptions.CaseNotFound(case_number)
        
        # Find the case item
        case_item = next((item for item in case.items if item.item_id == item_id), None)
        if not case_item:
            raise exceptions.ItemNotFound(item_id)
        
        # Complete production (domain method will validate)
        case_item.complete_production(actual_time)
        
        if quality_notes:
            case_item.notes += f"\nProduction completed: {quality_notes}"
        
        # Check if all items are completed - if so, update case status
        all_items_completed = all(
            item.status == model.TaskStatus.COMPLETED for item in case.items
        )
        
        if all_items_completed and case.status == model.CaseStatus.IN_PROGRESS:
            case.status = model.CaseStatus.QUALITY_CHECK
        
        # Update case
        uow.cases.update(case)
        uow.track_case(case)
        uow.commit()


# Business Intelligence Services
def get_production_efficiency_metrics(
    date_from: datetime,
    date_to: datetime,
    department_id: Optional[int] = None,
    uow: uow.AbstractUnitOfWork = None
) -> Dict[str, Any]:
    """
    Use case: Get production efficiency metrics
    
    Args:
        date_from: Start date for metrics
        date_to: End date for metrics
        department_id: Optional department filter
        uow: Unit of Work instance
    
    Returns:
        Dictionary with efficiency metrics
    """
    with uow:
        # Get completed cases in date range
        completed_cases = uow.cases.search_cases({
            'status': 'completed',
            'date_from': date_from,
            'date_to': date_to
        })
        
        if department_id:
            completed_cases = [
                case for case in completed_cases 
                if case.responsible_department_id == department_id
            ]
        
        # Calculate metrics
        total_cases = len(completed_cases)
        on_time_cases = [case for case in completed_cases if not case.is_overdue()]
        on_time_percentage = (len(on_time_cases) / total_cases * 100) if total_cases > 0 else 0
        
        # Calculate average completion time
        completion_times = []
        for case in completed_cases:
            if case.received_date_time and case.actual_completion:
                completion_time = case.actual_completion - case.received_date_time
                completion_times.append(completion_time.total_seconds() / 3600)  # Convert to hours
        
        avg_completion_hours = sum(completion_times) / len(completion_times) if completion_times else 0
        
        # Revision rate
        cases_with_revisions = [case for case in completed_cases if case.revision_count > 0]
        revision_rate = (len(cases_with_revisions) / total_cases * 100) if total_cases > 0 else 0
        
        return {
            'total_completed_cases': total_cases,
            'on_time_percentage': round(on_time_percentage, 2),
            'average_completion_hours': round(avg_completion_hours, 2),
            'revision_rate_percentage': round(revision_rate, 2),
            'cases_with_revisions': len(cases_with_revisions),
            'period': {
                'from': date_from.isoformat(),
                'to': date_to.isoformat()
            }
        }


def get_department_workload(
    department_id: Optional[int] = None,
    uow: uow.AbstractUnitOfWork = None
) -> Dict[str, Any]:
    """
    Use case: Get current workload by department
    
    Args:
        department_id: Optional specific department
        uow: Unit of Work instance
    
    Returns:
        Dictionary with workload information
    """
    with uow:
        # Get active cases
        active_cases = uow.cases.list_active_cases()
        
        if department_id:
            active_cases = [
                case for case in active_cases 
                if case.responsible_department_id == department_id
            ]
        
        # Get pending tasks
        pending_tasks = uow.tasks.list_pending_tasks()
        in_progress_tasks = uow.tasks.list_tasks_by_status(model.TaskStatus.IN_PROGRESS)
        
        # Filter by department if specified
        if department_id:
            all_tasks = pending_tasks + in_progress_tasks
            dept_cases = [case.case_number for case in active_cases]
            pending_tasks = [task for task in pending_tasks if task.case_number in dept_cases]
            in_progress_tasks = [task for task in in_progress_tasks if task.case_number in dept_cases]
        
        # Calculate workload metrics
        total_active_cases = len(active_cases)
        urgent_cases = [case for case in active_cases if case.priority == model.Priority.URGENT]
        overdue_cases = [case for case in active_cases if case.is_overdue()]
        
        return {
            'department_id': department_id,
            'active_cases_count': total_active_cases,
            'urgent_cases_count': len(urgent_cases),
            'overdue_cases_count': len(overdue_cases),
            'pending_tasks_count': len(pending_tasks),
            'in_progress_tasks_count': len(in_progress_tasks),
            'workload_score': _calculate_workload_score(
                total_active_cases, len(urgent_cases), len(overdue_cases), 
                len(pending_tasks), len(in_progress_tasks)
            )
        }


def _calculate_workload_score(
    active_cases: int, 
    urgent_cases: int, 
    overdue_cases: int,
    pending_tasks: int, 
    in_progress_tasks: int
) -> int:
    """
    Calculate a workload score (0-100) based on various metrics
    """
    # Simple scoring algorithm - can be made more sophisticated
    score = (
        active_cases * 2 +
        urgent_cases * 5 +
        overdue_cases * 10 +
        pending_tasks * 1 +
        in_progress_tasks * 3
    )
    
    # Normalize to 0-100 scale (this is simplified)
    return min(score, 100)


# Integration Services (for external systems)
def get_case_for_billing(
    case_number: int,
    uow: uow.AbstractUnitOfWork = None
) -> Dict[str, Any]:
    """
    Use case: Get case data formatted for billing system
    
    Args:
        case_number: Case to get billing data for
        uow: Unit of Work instance
    
    Returns:
        Dictionary with billing-relevant case data
    
    Raises:
        CaseNotFound: If case doesn't exist
    """
    with uow:
        case = uow.cases.get_by_case_number(case_number)
        if not case:
            raise exceptions.CaseNotFound(case_number)
        
        # Get dentist and patient details
        dentist = uow.dentists.get_by_dentist_id(case.dentist_id)
        patient = None
        if case.patient_id:
            patient = uow.patients.get_by_patient_id(case.patient_id)
        
        return {
            'case_number': case.case_number,
            'status': case.status.value,
            'created_at': case.created_at.isoformat(),
            'completed_at': case.actual_completion.isoformat() if case.actual_completion else None,
            'dentist': {
                'id': dentist.dentist_id,
                'name': dentist.name.full_name,
                'clinic': dentist.clinic_name,
                'contact': {
                    'phone': dentist.contact.phone,
                    'email': dentist.contact.email
                }
            } if dentist else None,
            'patient': {
                'id': patient.patient_id,
                'name': patient.name.full_name
            } if patient else None,
            'items': [
                {
                    'item_id': item.item_id,
                    'quantity': item.quantity,
                    'status': item.status.value,
                    'estimated_time_hours': item.estimated_time.total_seconds() / 3600,
                    'actual_time_hours': item.actual_time.total_seconds() / 3600 if item.actual_time else None
                }
                for item in case.items
            ],
            'cost_estimate': {
                'amount': float(case.cost_estimate.amount),
                'currency': case.cost_estimate.currency
            } if case.cost_estimate else None,
            'revision_count': case.revision_count,
            'notes': case.notes
        }


# Command Pattern Integration
def handle_command(command: commands.Command, uow: uow.AbstractUnitOfWork = None) -> Any:
    """
    Use case: Handle domain commands using command pattern
    
    Args:
        command: Domain command to handle
        uow: Unit of Work instance
    
    Returns:
        Result of command execution
    
    Raises:
        NotImplementedError: If command handler is not implemented
    """
    # Command handlers mapping
    handlers = {
        commands.CreateCase: _handle_create_case_command,
        commands.StartCase: _handle_start_case_command,
        commands.CompleteCase: _handle_complete_case_command,
        commands.AddItemToCase: _handle_add_item_command,
        commands.AssignTask: _handle_assign_task_command,
        commands.StartTask: _handle_start_task_command,
        commands.CompleteTask: _handle_complete_task_command,
        # Add more command handlers as needed
    }
    
    handler = handlers.get(type(command))
    if not handler:
        raise NotImplementedError(f"No handler for command {type(command).__name__}")
    
    return handler(command, uow)


# Command Handlers (internal functions)
def _handle_create_case_command(command: commands.CreateCase, uow: uow.AbstractUnitOfWork) -> int:
    """Handle CreateCase command"""
    return create_new_case(
        dentist_id=command.dentist_id,
        patient_id=command.patient_id,
        deadline=command.deadline,
        priority=command.priority,
        notes=command.notes,
        special_requirements=command.special_requirements,
        workflow_template_id=command.workflow_template_id,
        uow=uow
    )


def _handle_start_case_command(command: commands.StartCase, uow: uow.AbstractUnitOfWork) -> None:
    """Handle StartCase command"""
    start_case(
        case_number=command.case_number,
        started_by=command.issued_by,
        uow=uow
    )


def _handle_complete_case_command(command: commands.CompleteCase, uow: uow.AbstractUnitOfWork) -> None:
    """Handle CompleteCase command"""
    complete_case(
        case_number=command.case_number,
        completed_by=command.issued_by,
        completion_notes=command.completion_notes,
        uow=uow
    )


def _handle_add_item_command(command: commands.AddItemToCase, uow: uow.AbstractUnitOfWork) -> None:
    """Handle AddItemToCase command"""
    add_item_to_case(
        case_number=command.case_number,
        item_id=command.item_id,
        quantity=command.quantity,
        unit_id=command.unit_id,
        estimated_time=command.estimated_time,
        special_instructions=command.special_instructions,
        added_by=command.issued_by,
        uow=uow
    )


def _handle_assign_task_command(command: commands.AssignTask, uow: uow.AbstractUnitOfWork) -> None:
    """Handle AssignTask command"""
    assign_task(
        task_id=command.task_id,
        assigned_to=command.assigned_to,
        assignment_notes=command.assignment_notes,
        assigned_by=command.issued_by,
        uow=uow
    )


def _handle_start_task_command(command: commands.StartTask, uow: uow.AbstractUnitOfWork) -> None:
    """Handle StartTask command"""
    start_task(
        task_id=command.task_id,
        started_by=command.issued_by,
        start_notes=command.start_notes,
        uow=uow
    )


def _handle_complete_task_command(command: commands.CompleteTask, uow: uow.AbstractUnitOfWork) -> None:
    """Handle CompleteTask command"""
    complete_task(
        task_id=command.task_id,
        completed_by=command.issued_by,
        completion_notes=command.completion_notes,
        quality_check_passed=command.quality_check_passed,
        uow=uow
    )
