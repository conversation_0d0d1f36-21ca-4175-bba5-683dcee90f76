# DENTAL LAB DDD - Pytest Configuration
# Global test configuration and fixtures

import pytest
from unittest.mock import Mock
import os
import sys
from datetime import datetime, timedelta

# Add src to Python path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Django setup for tests
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dental_lab_web.settings')

try:
    import django
    django.setup()
except ImportError:
    # Django not yet available during early test collection
    pass

# Import domain models for fixtures
from dental_lab.domain import model
from dental_lab.service_layer import uow


# Domain fixtures
@pytest.fixture
def sample_case():
    """Create a sample case for testing"""
    return model.Case(
        case_number=1001,
        dentist_id=1,
        patient_id=1,
        deadline=datetime.now() + timedelta(days=7)
    )


@pytest.fixture
def sample_patient():
    """Create a sample patient for testing"""
    return model.Patient(
        patient_id=1,
        first_name="<PERSON>",
        last_name="<PERSON><PERSON>",
        dentist_id=1,
        phone_number="************",
        email="<EMAIL>"
    )


@pytest.fixture
def sample_dentist():
    """Create a sample dentist for testing"""
    return model.Dentist(
        dentist_id=1,
        first_name="Dr. Jane",
        last_name="Smith",
        clinic_name="Smile Clinic",
        phone_number="************"
    )


# Mock repositories for unit testing
class FakeCaseRepository:
    def __init__(self):
        self._cases = {}
        self._next_id = 1000

    def add(self, case):
        self._cases[case.case_number] = case
        return case

    def get_by_case_number(self, case_number):
        return self._cases.get(case_number)

    def list_active_cases(self):
        return [case for case in self._cases.values() 
                if case.status != model.CaseStatus.COMPLETED]

    def list_overdue_cases(self):
        return [case for case in self._cases.values() 
                if case.is_overdue()]


class FakePatientRepository:
    def __init__(self):
        self._patients = {}

    def add(self, patient):
        self._patients[patient.patient_id] = patient
        return patient

    def get(self, patient_id):
        return self._patients.get(patient_id)

    def get_by_dentist(self, dentist_id):
        return [p for p in self._patients.values() 
                if p.dentist_id == dentist_id]


class FakeDentistRepository:
    def __init__(self):
        self._dentists = {}

    def add(self, dentist):
        self._dentists[dentist.dentist_id] = dentist
        return dentist

    def get(self, dentist_id):
        return self._dentists.get(dentist_id)


class FakeUnitOfWork:
    def __init__(self):
        self.cases = FakeCaseRepository()
        self.patients = FakePatientRepository()
        self.dentists = FakeDentistRepository()
        self.committed = False

    def __enter__(self):
        return self

    def __exit__(self, *args):
        pass

    def commit(self):
        self.committed = True

    def rollback(self):
        pass


@pytest.fixture
def fake_uow():
    """Create a fake unit of work for testing"""
    return FakeUnitOfWork()


@pytest.fixture
def populated_fake_uow(fake_uow, sample_dentist, sample_patient):
    """Create a fake unit of work with sample data"""
    fake_uow.dentists.add(sample_dentist)
    fake_uow.patients.add(sample_patient)
    return fake_uow


# Pytest markers
def pytest_configure(config):
    """Configure pytest markers"""
    config.addinivalue_line(
        "markers", "unit: Unit tests for domain logic"
    )
    config.addinivalue_line(
        "markers", "integration: Integration tests for adapters"
    )
    config.addinivalue_line(
        "markers", "e2e: End-to-end tests"
    )
    config.addinivalue_line(
        "markers", "slow: Tests that take longer to run"
    )