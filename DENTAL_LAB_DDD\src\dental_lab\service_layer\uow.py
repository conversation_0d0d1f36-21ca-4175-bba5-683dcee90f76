# DENTAL LAB DDD - Unit of Work Pattern
# Manages transactions and coordinates repository operations

import abc
from typing import Optional, Set, List, Any
from dental_lab.adapters import repository, django_repositories
from dental_lab.domain import events


class AbstractUnitOfWork(abc.ABC):
    """
    Abstract Unit of Work pattern
    Coordinates multiple repository operations in a single transaction
    """
    
    # Repository properties
    cases: repository.AbstractCaseRepository
    patients: repository.AbstractPatientRepository
    dentists: repository.AbstractDentistRepository
    tasks: repository.AbstractTaskRepository
    case_items: repository.AbstractCaseItemRepository
    
    def __init__(self):
        self.committed = False
        self._events_to_publish: List[events.DomainEvent] = []
    
    def __enter__(self):
        return self
    
    def __exit__(self, *args):
        if not self.committed:
            self.rollback()
    
    @abc.abstractmethod
    def commit(self):
        """Commit all changes in this unit of work"""
        self.committed = True
    
    @abc.abstractmethod
    def rollback(self):
        """Rollback all changes in this unit of work"""
        pass
    
    def collect_new_events(self):
        """Collect domain events from aggregates"""
        events_to_publish = []
        
        # Collect events from cases (our main aggregates)
        for case in self._get_tracked_aggregates():
            while case.events:
                events_to_publish.append(case.events.pop(0))
        
        return events_to_publish
    
    @abc.abstractmethod
    def _get_tracked_aggregates(self):
        """Get all tracked aggregates that might have events"""
        pass


class DjangoUnitOfWork(AbstractUnitOfWork):
    """
    Django implementation of Unit of Work
    Uses Django's transaction management
    """
    
    def __init__(self):
        super().__init__()
        self.cases = django_repositories.DjangoCaseRepository()
        self.patients = django_repositories.DjangoPatientRepository()
        self.dentists = django_repositories.DjangoDentistRepository()
        self.tasks = django_repositories.DjangoTaskRepository()
        self.case_items = django_repositories.DjangoCaseItemRepository()
        
        # Track loaded aggregates for event collection
        self._loaded_cases: Set[int] = set()
        self._case_cache: dict = {}
    
    def __enter__(self):
        # Start Django transaction
        from django.db import transaction
        self._transaction = transaction.atomic()
        self._transaction.__enter__()
        return super().__enter__()
    
    def __exit__(self, *args):
        if hasattr(self, '_transaction'):
            try:
                # Collect events before committing/rolling back
                if not self.committed:
                    self._events_to_publish = self.collect_new_events()
                
                self._transaction.__exit__(*args)
            finally:
                # Clean up
                if hasattr(self, '_transaction'):
                    del self._transaction
        
        return super().__exit__(*args)
    
    def commit(self):
        """Commit the Django transaction"""
        # Collect events before committing
        self._events_to_publish = self.collect_new_events()
        super().commit()
    
    def rollback(self):
        """Django handles rollback automatically if transaction fails"""
        pass
    
    def _get_tracked_aggregates(self):
        """Get all loaded cases (our main aggregates)"""
        aggregates = []
        
        # Get cases from cache
        for case_number in self._loaded_cases:
            case = self._case_cache.get(case_number)
            if case:
                aggregates.append(case)
        
        return aggregates
    
    def track_case(self, case):
        """Track a case for event collection"""
        self._loaded_cases.add(case.case_number)
        self._case_cache[case.case_number] = case
    
    def get_events_to_publish(self) -> List[events.DomainEvent]:
        """Get events that should be published after commit"""
        return self._events_to_publish.copy()


class FakeUnitOfWork(AbstractUnitOfWork):
    """
    Fake Unit of Work for testing
    Uses in-memory repositories
    """
    
    def __init__(self):
        super().__init__()
        self.cases = FakeCaseRepository()
        self.patients = FakePatientRepository()
        self.dentists = FakeDentistRepository()
        self.tasks = FakeTaskRepository()
        self.case_items = FakeCaseItemRepository()
        
        # Track aggregates
        self._aggregates = []
    
    def commit(self):
        """Fake commit - just mark as committed"""
        self._events_to_publish = self.collect_new_events()
        super().commit()
    
    def rollback(self):
        """Fake rollback - clear repositories"""
        self.cases._entities.clear()
        self.patients._entities.clear()
        self.dentists._entities.clear()
        self.tasks._entities.clear()
        self.case_items._entities.clear()
        self._aggregates.clear()
    
    def _get_tracked_aggregates(self):
        """Get tracked aggregates"""
        return self._aggregates
    
    def track_aggregate(self, aggregate):
        """Track an aggregate for event collection"""
        if aggregate not in self._aggregates:
            self._aggregates.append(aggregate)
    
    def track_case(self, case):
        """Track a case (compatibility method)"""
        self.track_aggregate(case)


# Fake repositories for testing
class FakeRepository:
    """Base fake repository"""
    
    def __init__(self):
        self._entities = {}
        self._next_id = 1
    
    def add(self, entity):
        entity_id = getattr(entity, self._id_field)
        if entity_id is None:
            entity_id = self._next_id
            setattr(entity, self._id_field, entity_id)
            self._next_id += 1
        
        self._entities[entity_id] = entity
        return entity
    
    def get(self, entity_id):
        return self._entities.get(entity_id)
    
    def update(self, entity):
        entity_id = getattr(entity, self._id_field)
        if entity_id in self._entities:
            self._entities[entity_id] = entity
        else:
            raise ValueError(f"Entity with {self._id_field}={entity_id} not found")
    
    def delete(self, entity_id):
        if entity_id in self._entities:
            del self._entities[entity_id]
        else:
            raise ValueError(f"Entity with {self._id_field}={entity_id} not found")
    
    def list_all(self):
        return list(self._entities.values())


class FakeCaseRepository(FakeRepository, repository.AbstractCaseRepository):
    """Fake case repository for testing"""
    
    def __init__(self):
        super().__init__()
        self._id_field = 'case_number'
        self._next_id = 1000  # Start case numbers at 1000
    
    def get_by_case_number(self, case_number):
        return self.get(case_number)
    
    def list_active_cases(self):
        from dental_lab.domain.model import CaseStatus
        return [
            case for case in self._entities.values()
            if case.status not in [CaseStatus.COMPLETED, CaseStatus.CANCELLED]
        ]
    
    def list_overdue_cases(self):
        return [case for case in self._entities.values() if case.is_overdue()]
    
    def list_cases_by_dentist(self, dentist_id):
        return [
            case for case in self._entities.values()
            if case.dentist_id == dentist_id
        ]
    
    def list_cases_by_status(self, status):
        return [
            case for case in self._entities.values()
            if case.status == status
        ]
    
    def list_cases_by_department(self, department_id):
        return [
            case for case in self._entities.values()
            if case.responsible_department_id == department_id
        ]
    
    def list_cases_due_soon(self, days=3):
        from datetime import datetime, timedelta
        cutoff_date = datetime.now() + timedelta(days=days)
        return [
            case for case in self._entities.values()
            if case.deadline and case.deadline <= cutoff_date and not case.is_overdue()
        ]
    
    def search_cases(self, criteria):
        results = list(self._entities.values())
        
        if 'status' in criteria:
            results = [case for case in results if case.status.value == criteria['status']]
        
        if 'dentist_id' in criteria:
            results = [case for case in results if case.dentist_id == criteria['dentist_id']]
        
        if 'priority' in criteria:
            results = [case for case in results if case.priority.value == criteria['priority']]
        
        return results
    
    def get_next_case_number(self):
        if self._entities:
            return max(self._entities.keys()) + 1
        return self._next_id
    
    def count_cases_by_status(self):
        counts = {}
        for case in self._entities.values():
            status = case.status.value
            counts[status] = counts.get(status, 0) + 1
        return counts


class FakePatientRepository(FakeRepository, repository.AbstractPatientRepository):
    """Fake patient repository for testing"""
    
    def __init__(self):
        super().__init__()
        self._id_field = 'patient_id'
    
    def get_by_patient_id(self, patient_id):
        return self.get(patient_id)
    
    def list_patients_by_dentist(self, dentist_id):
        return [
            patient for patient in self._entities.values()
            if patient.dentist_id == dentist_id
        ]
    
    def search_patients(self, name_query, dentist_id=None):
        results = [
            patient for patient in self._entities.values()
            if (name_query.lower() in patient.name.first_name.lower() or 
                name_query.lower() in patient.name.last_name.lower())
        ]
        
        if dentist_id is not None:
            results = [p for p in results if p.dentist_id == dentist_id]
        
        return results
    
    def get_patient_with_cases(self, patient_id):
        return self.get(patient_id)
    
    def list_patients_with_active_cases(self):
        # This would require cross-repository logic in a real implementation
        return list(self._entities.values())


class FakeDentistRepository(FakeRepository, repository.AbstractDentistRepository):
    """Fake dentist repository for testing"""
    
    def __init__(self):
        super().__init__()
        self._id_field = 'dentist_id'
    
    def get_by_dentist_id(self, dentist_id):
        return self.get(dentist_id)
    
    def get_by_user_id(self, user_id):
        return next(
            (dentist for dentist in self._entities.values() 
             if dentist.user_id == user_id),
            None
        )
    
    def list_all_dentists(self):
        return list(self._entities.values())
    
    def search_dentists(self, name_query):
        return [
            dentist for dentist in self._entities.values()
            if (name_query.lower() in dentist.name.first_name.lower() or 
                name_query.lower() in dentist.name.last_name.lower() or
                name_query.lower() in dentist.clinic_name.lower())
        ]
    
    def get_dentist_with_patients(self, dentist_id):
        return self.get(dentist_id)
    
    def list_dentists_with_active_cases(self):
        return list(self._entities.values())


class FakeTaskRepository(FakeRepository, repository.AbstractTaskRepository):
    """Fake task repository for testing"""
    
    def __init__(self):
        super().__init__()
        self._id_field = 'task_id'
    
    def get_by_task_id(self, task_id):
        return self.get(task_id)
    
    def list_tasks_by_case(self, case_number):
        return [
            task for task in self._entities.values()
            if task.case_number == case_number
        ]
    
    def list_tasks_by_assignee(self, assignee_id):
        return [
            task for task in self._entities.values()
            if task.assigned_to == assignee_id
        ]
    
    def list_tasks_by_status(self, status):
        return [
            task for task in self._entities.values()
            if task.status == status
        ]
    
    def list_overdue_tasks(self):
        return [task for task in self._entities.values() if task.is_overdue]
    
    def list_pending_tasks(self, assignee_id=None):
        from dental_lab.domain.model import TaskStatus
        tasks = [
            task for task in self._entities.values()
            if task.status == TaskStatus.PENDING
        ]
        
        if assignee_id is not None:
            tasks = [task for task in tasks if task.assigned_to == assignee_id]
        
        return tasks
    
    def list_blocked_tasks(self):
        from dental_lab.domain.model import TaskStatus
        return [
            task for task in self._entities.values()
            if task.status == TaskStatus.BLOCKED
        ]
    
    def list_tasks_by_workflow_stage(self, stage_id):
        return [
            task for task in self._entities.values()
            if task.workflow_stage_id == stage_id
        ]
    
    def get_next_task_id(self):
        if self._entities:
            return max(self._entities.keys()) + 1
        return 1


class FakeCaseItemRepository(FakeRepository, repository.AbstractCaseItemRepository):
    """Fake case item repository for testing"""
    
    def __init__(self):
        super().__init__()
        self._id_field = 'item_id'
        self._items_by_case = {}  # case_number -> {item_id -> CaseItem}
    
    def add(self, case_item):
        case_number = case_item.case_number
        item_id = case_item.item_id
        
        if case_number not in self._items_by_case:
            self._items_by_case[case_number] = {}
        
        self._items_by_case[case_number][item_id] = case_item
        return case_item
    
    def get(self, case_item_id):
        # For simplicity, assume case_item_id is a tuple (case_number, item_id)
        if isinstance(case_item_id, tuple):
            case_number, item_id = case_item_id
            return self.get_case_item(case_number, item_id)
        return None
    
    def list_items_by_case(self, case_number):
        return list(self._items_by_case.get(case_number, {}).values())
    
    def get_case_item(self, case_number, item_id):
        case_items = self._items_by_case.get(case_number, {})
        return case_items.get(item_id)
    
    def list_items_by_status(self, status):
        items = []
        for case_items in self._items_by_case.values():
            items.extend([
                item for item in case_items.values()
                if item.status == status
            ])
        return items
    
    def list_items_in_production(self):
        from dental_lab.domain.model import TaskStatus
        return self.list_items_by_status(TaskStatus.IN_PROGRESS)
    
    def list_delayed_items(self):
        items = []
        for case_items in self._items_by_case.values():
            items.extend([
                item for item in case_items.values()
                if item.is_delayed
            ])
        return items
    
    def update(self, case_item):
        case_number = case_item.case_number
        item_id = case_item.item_id
        
        if case_number in self._items_by_case and item_id in self._items_by_case[case_number]:
            self._items_by_case[case_number][item_id] = case_item
        else:
            raise ValueError(f"CaseItem not found: case {case_number}, item {item_id}")
    
    def delete(self, case_item_id):
        # Implement if needed
        pass