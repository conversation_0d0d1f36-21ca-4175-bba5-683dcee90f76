[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "dental-lab-ddd"
version = "0.1.0"
description = "Dental Laboratory Management System built with DDD and Clean Architecture"
authors = [
    {name = "Dental Lab Team", email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.9"
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Healthcare Industry",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
]

dependencies = [
    "Django>=4.2.0,<5.0",
    "djangorestframework>=3.14.0",
    "psycopg2-binary>=2.9.0",
    "python-dotenv>=0.19.0",
    "redis>=4.3.0",
    "celery>=5.2.0",
    "python-dateutil>=2.8.0",
    "Pillow>=9.0.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-django>=4.5.0",
    "pytest-cov>=4.0.0",
    "factory-boy>=3.2.0",
    "mypy>=1.0.0",
    "black>=22.0.0",
    "flake8>=5.0.0",
    "isort>=5.10.0",
    "ipython>=8.0.0",
]

prod = [
    "gunicorn>=20.1.0",
    "whitenoise>=6.0.0",
]

[project.urls]
Repository = "https://github.com/your-org/dental-lab-ddd"
Documentation = "https://dental-lab-ddd.readthedocs.io/"

[tool.setuptools.packages.find]
where = ["src"]
include = ["dental_lab*"]

[tool.black]
line-length = 88
target-version = ['py39']
include = '\.pyi?$'
exclude = '''
/(
    \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | _build
  | buck-out
  | build
  | dist
  | migrations
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
skip = ["migrations"]

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
check_untyped_defs = true
warn_redundant_casts = true
warn_unused_ignores = true
show_error_codes = true

[[tool.mypy.overrides]]
module = [
    "django.*",
    "pytest.*",
    "factory.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
DJANGO_SETTINGS_MODULE = "dental_lab_web.settings"
python_files = ["tests.py", "test_*.py", "*_tests.py"]
addopts = [
    "--tb=short",
    "--strict-markers",
    "--disable-warnings",
    "--cov=src/dental_lab",
    "--cov-report=term-missing",
    "--cov-report=html",
]
testpaths = ["tests"]
markers = [
    "unit: Unit tests for domain logic",
    "integration: Integration tests for adapters",
    "e2e: End-to-end tests",
    "slow: Tests that take longer to run",
]

[tool.coverage.run]
source = ["src/dental_lab"]
omit = [
    "*/migrations/*",
    "*/tests/*",
    "*/venv/*",
    "*/env/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "raise AssertionError",
    "raise NotImplementedError",
]