# DENTAL LAB DDD - Requirements
# Core dependencies for the dental lab management system

# Web Framework
Django>=4.2.0,<5.0
djangorestframework>=3.14.0

# Database
psycopg2-binary>=2.9.0  # PostgreSQL adapter

# Testing
pytest>=7.0.0
pytest-django>=4.5.0
pytest-cov>=4.0.0
factory-boy>=3.2.0

# Code Quality
mypy>=1.0.0
black>=22.0.0
flake8>=5.0.0
isort>=5.10.0

# Development Tools
python-dotenv>=0.19.0
ipython>=8.0.0

# Production
gunicorn>=20.1.0
whitenoise>=6.0.0

# Async & Messaging (for future event-driven features)
redis>=4.3.0
celery>=5.2.0

# Utilities
python-dateutil>=2.8.0
Pillow>=9.0.0  # For image handling in attachments

# API Documentation (optional)
drf-yasg>=1.21.0