# DENTAL LAB DDD - Pytest Configuration

[tool:pytest]
DJANGO_SETTINGS_MODULE = dental_lab_web.settings
python_files = tests.py test_*.py *_tests.py
testpaths = tests
addopts = 
    --tb=short
    --strict-markers
    --disable-warnings
    --reuse-db
    --nomigrations
    -v

markers =
    unit: Unit tests for domain logic (fast, no external dependencies)
    integration: Integration tests for adapters (database, external services)
    e2e: End-to-end tests (full application workflow)
    slow: Tests that take longer to run
    django_db: Tests that require database access

filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning