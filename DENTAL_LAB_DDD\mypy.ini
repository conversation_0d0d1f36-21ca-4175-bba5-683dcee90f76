# DENTAL LAB DDD - MyPy Configuration

[mypy]
python_version = 3.9
warn_return_any = True
warn_unused_configs = True
disallow_untyped_defs = True
check_untyped_defs = True
warn_redundant_casts = True
warn_unused_ignores = True
show_error_codes = True
strict_optional = True
no_implicit_optional = True

# Modules to check
files = src/dental_lab

# Ignore missing imports for external libraries
[mypy-django.*]
ignore_missing_imports = True

[mypy-pytest.*]
ignore_missing_imports = True

[mypy-factory.*]
ignore_missing_imports = True

[mypy-redis.*]
ignore_missing_imports = True

[mypy-celery.*]
ignore_missing_imports = True

[mypy-PIL.*]
ignore_missing_imports = True

# Domain layer should have strict typing
[mypy-dental_lab.domain.*]
disallow_any_generics = True
disallow_subclassing_any = True
disallow_untyped_calls = True
disallow_incomplete_defs = True
warn_unreachable = True