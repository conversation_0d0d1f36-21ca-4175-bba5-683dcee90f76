# DENTAL LAB DDD - Django ORM Models (Thin Models)
# These models define ONLY database structure - NO business logic

from django.db import models
from django.utils import timezone
from django.core.validators import MinValueValidator, MaxValueValidator
from django.contrib.auth import get_user_model
from datetime import <PERSON><PERSON><PERSON>
from decimal import Decimal
import json

from dental_lab.domain import model as domain_model

User = get_user_model()


class Case(models.Model):
    """
    ORM model for Case - ONLY database structure
    Business logic is in domain.model.Case
    """
    
    STATUS_CHOICES = [
        ('pending_acceptance', 'Pending Acceptance'),
        ('on_hold', 'On Hold'),
        ('in_progress', 'In Progress'),
        ('quality_check', 'Quality Check'),
        ('revision_needed', 'Revision Needed'),
        ('ready_to_ship', 'Ready to Ship'),
        ('shipped', 'Shipped'),
        ('delivered', 'Delivered'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
    ]
    
    PRIORITY_CHOICES = [
        (1, 'Low'),
        (2, 'Normal'),
        (3, 'High'),
        (4, 'Urgent'),
    ]
    
    # Primary key
    case_number = models.AutoField(primary_key=True)
    
    # Relationships (stored as IDs only)
    dentist_id = models.IntegerField(db_index=True)
    patient_id = models.IntegerField(null=True, blank=True, db_index=True)
    workflow_template_id = models.IntegerField(null=True, blank=True)
    current_stage_id = models.IntegerField(null=True, blank=True)
    responsible_department_id = models.IntegerField(null=True, blank=True)
    
    # Status and priority
    status = models.CharField(max_length=30, choices=STATUS_CHOICES, default='pending_acceptance', db_index=True)
    priority = models.PositiveSmallIntegerField(choices=PRIORITY_CHOICES, default=2)
    
    # Timestamps
    created_at = models.DateTimeField(default=timezone.now, db_index=True)
    received_date_time = models.DateTimeField(null=True, blank=True)
    estimated_completion = models.DateTimeField(null=True, blank=True)
    actual_completion = models.DateTimeField(null=True, blank=True)
    ship_date_time = models.DateTimeField(null=True, blank=True)
    delivery_date = models.DateTimeField(null=True, blank=True)
    deadline = models.DateTimeField(null=True, blank=True, db_index=True)
    
    # Progress tracking
    progress_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    revision_count = models.PositiveIntegerField(default=0)
    
    # Collections stored as JSON or comma-separated values
    selected_teeth = models.JSONField(default=list, blank=True)  # List of tooth numbers
    assigned_technician_ids = models.JSONField(default=list, blank=True)  # List of user IDs
    
    # Quality and feedback
    quality_checks = models.JSONField(default=dict, blank=True)
    patient_feedback = models.DecimalField(max_digits=3, decimal_places=2, null=True, blank=True)
    dentist_feedback = models.DecimalField(max_digits=3, decimal_places=2, null=True, blank=True)
    
    # Costs (stored as decimal, currency separate)
    cost_estimate_amount = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    cost_estimate_currency = models.CharField(max_length=3, default='EUR')
    
    # Text fields
    notes = models.TextField(blank=True)
    special_requirements = models.TextField(blank=True)
    teeth_color = models.CharField(max_length=30, blank=True)
    delay_reason = models.TextField(blank=True)
    
    class Meta:
        db_table = 'cases'
        indexes = [
            models.Index(fields=['status']),
            models.Index(fields=['created_at']),
            models.Index(fields=['deadline']),
            models.Index(fields=['dentist_id']),
            models.Index(fields=['responsible_department_id']),
            models.Index(fields=['dentist_id', 'status']),
        ]
        ordering = ['-created_at']
    
    def __str__(self):
        return f"Case #{self.case_number}"
    
    # Domain Model Conversion Methods
    @staticmethod
    def update_from_domain(case: domain_model.Case):
        """Update or create ORM model from domain model"""
        try:
            orm_case = Case.objects.get(case_number=case.case_number)
        except Case.DoesNotExist:
            orm_case = Case(case_number=case.case_number)
        
        # Basic fields
        orm_case.dentist_id = case.dentist_id
        orm_case.patient_id = case.patient_id
        orm_case.status = case.status.value if hasattr(case.status, 'value') else case.status
        orm_case.priority = case.priority.value if hasattr(case.priority, 'value') else case.priority
        
        # Timestamps
        orm_case.created_at = case.created_at
        orm_case.received_date_time = case.received_date_time
        orm_case.estimated_completion = case.estimated_completion
        orm_case.actual_completion = case.actual_completion
        orm_case.ship_date_time = case.ship_date_time
        orm_case.delivery_date = case.delivery_date
        orm_case.deadline = case.deadline
        
        # Progress and workflow
        orm_case.progress_percentage = case.progress_percentage
        orm_case.revision_count = case.revision_count
        orm_case.workflow_template_id = case.workflow_template_id
        orm_case.current_stage_id = case.current_stage_id
        orm_case.responsible_department_id = case.responsible_department_id
        
        # Collections
        orm_case.selected_teeth = list(case.selected_teeth)
        orm_case.assigned_technician_ids = list(case.assigned_technician_ids)
        orm_case.quality_checks = case.quality_checks
        
        # Feedback
        orm_case.patient_feedback = case.patient_feedback
        orm_case.dentist_feedback = case.dentist_feedback
        
        # Cost
        if case.cost_estimate:
            orm_case.cost_estimate_amount = case.cost_estimate.amount
            orm_case.cost_estimate_currency = case.cost_estimate.currency
        
        # Text fields
        orm_case.notes = case.notes
        orm_case.special_requirements = case.special_requirements
        orm_case.teeth_color = case.teeth_color
        orm_case.delay_reason = case.delay_reason
        
        orm_case.save()
        
        # Update related items and tasks
        CaseItem.update_items_from_domain(case)
        Task.update_tasks_from_domain(case)
        
        return orm_case
    
    def to_domain(self) -> domain_model.Case:
        """Convert ORM model to domain model"""
        # Create cost estimate if available
        cost_estimate = None
        if self.cost_estimate_amount is not None:
            cost_estimate = domain_model.Money(
                amount=self.cost_estimate_amount,
                currency=self.cost_estimate_currency
            )
        
        # Create domain case
        domain_case = domain_model.Case(
            case_number=self.case_number,
            dentist_id=self.dentist_id,
            patient_id=self.patient_id,
            status=domain_model.CaseStatus(self.status),
            deadline=self.deadline,
            created_at=self.created_at
        )
        
        # Set additional properties
        domain_case.priority = domain_model.Priority(self.priority)
        domain_case.received_date_time = self.received_date_time
        domain_case.estimated_completion = self.estimated_completion
        domain_case.actual_completion = self.actual_completion
        domain_case.ship_date_time = self.ship_date_time
        domain_case.delivery_date = self.delivery_date
        
        domain_case.progress_percentage = self.progress_percentage
        domain_case.revision_count = self.revision_count
        domain_case.workflow_template_id = self.workflow_template_id
        domain_case.current_stage_id = self.current_stage_id
        domain_case.responsible_department_id = self.responsible_department_id
        
        domain_case.selected_teeth = set(self.selected_teeth)
        domain_case.assigned_technician_ids = set(self.assigned_technician_ids)
        domain_case.quality_checks = self.quality_checks
        
        domain_case.patient_feedback = self.patient_feedback
        domain_case.dentist_feedback = self.dentist_feedback
        domain_case.cost_estimate = cost_estimate
        
        domain_case.notes = self.notes
        domain_case.special_requirements = self.special_requirements
        domain_case.teeth_color = self.teeth_color
        domain_case.delay_reason = self.delay_reason
        
        # Load related items and tasks
        domain_case.items = [
            item.to_domain() for item in CaseItem.objects.filter(case_number=self.case_number)
        ]
        domain_case.tasks = [
            task.to_domain() for task in Task.objects.filter(case_number=self.case_number)
        ]
        
        # Clear events (they should be loaded separately if needed)
        domain_case.events = []
        
        return domain_case


class Patient(models.Model):
    """
    ORM model for Patient - ONLY database structure
    Business logic is in domain.model.Patient
    """
    
    patient_id = models.AutoField(primary_key=True)
    
    # Name fields
    first_name = models.CharField(max_length=50, db_index=True)
    last_name = models.CharField(max_length=50, db_index=True)
    
    # Relationships
    dentist_id = models.IntegerField(db_index=True)
    
    # Contact information
    phone_number = models.CharField(max_length=15, blank=True)
    email = models.EmailField(blank=True)
    address = models.TextField(blank=True)
    
    # Personal information
    date_of_birth = models.DateField(null=True, blank=True)
    
    # Metadata
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'patients'
        indexes = [
            models.Index(fields=['dentist_id']),
            models.Index(fields=['first_name', 'last_name']),
            models.Index(fields=['email']),
        ]
        ordering = ['first_name', 'last_name']
    
    def __str__(self):
        return f"{self.first_name} {self.last_name}".strip()
    
    # Domain Model Conversion Methods
    @staticmethod
    def update_from_domain(patient: domain_model.Patient):
        """Update or create ORM model from domain model"""
        try:
            orm_patient = Patient.objects.get(patient_id=patient.patient_id)
        except Patient.DoesNotExist:
            orm_patient = Patient(patient_id=patient.patient_id)
        
        # Name
        orm_patient.first_name = patient.name.first_name
        orm_patient.last_name = patient.name.last_name
        
        # Relationships
        orm_patient.dentist_id = patient.dentist_id
        
        # Contact information
        if patient.contact:
            orm_patient.phone_number = patient.contact.phone or ""
            orm_patient.email = patient.contact.email or ""
            orm_patient.address = patient.contact.address or ""
        
        # Personal information
        orm_patient.date_of_birth = patient.date_of_birth
        
        orm_patient.save()
        return orm_patient
    
    def to_domain(self) -> domain_model.Patient:
        """Convert ORM model to domain model"""
        # Create value objects
        name = domain_model.PersonName(
            first_name=self.first_name,
            last_name=self.last_name
        )
        
        contact = domain_model.ContactInfo(
            phone=self.phone_number or None,
            email=self.email or None,
            address=self.address or None
        )
        
        # Create domain patient
        domain_patient = domain_model.Patient(
            patient_id=self.patient_id,
            name=name,
            dentist_id=self.dentist_id,
            contact=contact,
            date_of_birth=self.date_of_birth
        )
        
        # Clear events
        domain_patient.events = []
        
        return domain_patient


class Dentist(models.Model):
    """
    ORM model for Dentist - ONLY database structure
    Business logic is in domain.model.Dentist
    """
    
    dentist_id = models.AutoField(primary_key=True)
    
    # Name fields
    first_name = models.CharField(max_length=50, db_index=True)
    last_name = models.CharField(max_length=50, db_index=True)
    
    # Business information
    clinic_name = models.CharField(max_length=100, db_index=True)
    
    # Contact information
    phone_number = models.CharField(max_length=15)
    email = models.EmailField(blank=True)
    address = models.TextField(blank=True)
    
    # Link to user account
    user_id = models.IntegerField(unique=True, null=True, blank=True)
    
    # Metadata
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)
    
    class Meta:
        db_table = 'dentists'
        indexes = [
            models.Index(fields=['clinic_name']),
            models.Index(fields=['first_name', 'last_name']),
            models.Index(fields=['user_id']),
        ]
        ordering = ['first_name', 'last_name']
    
    def __str__(self):
        return f"{self.first_name} {self.last_name}"
    
    # Domain Model Conversion Methods
    @staticmethod
    def update_from_domain(dentist: domain_model.Dentist):
        """Update or create ORM model from domain model"""
        try:
            orm_dentist = Dentist.objects.get(dentist_id=dentist.dentist_id)
        except Dentist.DoesNotExist:
            orm_dentist = Dentist(dentist_id=dentist.dentist_id)
        
        # Name
        orm_dentist.first_name = dentist.name.first_name
        orm_dentist.last_name = dentist.name.last_name
        
        # Business information
        orm_dentist.clinic_name = dentist.clinic_name
        
        # Contact information
        if dentist.contact:
            orm_dentist.phone_number = dentist.contact.phone or ""
            orm_dentist.email = dentist.contact.email or ""
            orm_dentist.address = dentist.contact.address or ""
        
        # User account link
        orm_dentist.user_id = dentist.user_id
        
        orm_dentist.save()
        return orm_dentist
    
    def to_domain(self) -> domain_model.Dentist:
        """Convert ORM model to domain model"""
        # Create value objects
        name = domain_model.PersonName(
            first_name=self.first_name,
            last_name=self.last_name
        )
        
        contact = domain_model.ContactInfo(
            phone=self.phone_number or None,
            email=self.email or None,
            address=self.address or None
        )
        
        # Create domain dentist
        domain_dentist = domain_model.Dentist(
            dentist_id=self.dentist_id,
            name=name,
            clinic_name=self.clinic_name,
            contact=contact,
            user_id=self.user_id
        )
        
        # Clear events
        domain_dentist.events = []
        
        return domain_dentist


class Task(models.Model):
    """
    ORM model for Task - ONLY database structure
    Business logic is in domain.model.Task
    """
    
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('in_progress', 'In Progress'),
        ('paused', 'Paused'),
        ('completed', 'Completed'),
        ('delayed', 'Delayed'),
        ('blocked', 'Blocked'),
        ('cancelled', 'Cancelled'),
        ('review', 'Under Review'),
    ]
    
    PRIORITY_CHOICES = [
        (1, 'Low'),
        (2, 'Normal'),
        (3, 'High'),
        (4, 'Urgent'),
    ]
    
    task_id = models.AutoField(primary_key=True)
    
    # Relationships
    case_number = models.IntegerField(db_index=True)
    case_item_id = models.IntegerField(null=True, blank=True)
    workflow_stage_id = models.IntegerField(null=True, blank=True)
    assigned_to = models.IntegerField(null=True, blank=True, db_index=True)
    created_by = models.IntegerField(null=True, blank=True)
    
    # Task details
    title = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending', db_index=True)
    priority = models.IntegerField(choices=PRIORITY_CHOICES, default=2)
    
    # Progress
    progress_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    
    # Time tracking (stored as seconds for precision)
    estimated_duration_seconds = models.BigIntegerField()
    actual_duration_seconds = models.BigIntegerField(null=True, blank=True)
    paused_time_seconds = models.BigIntegerField(default=0)
    
    # Timestamps
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    actual_start_time = models.DateTimeField(null=True, blank=True)
    actual_end_time = models.DateTimeField(null=True, blank=True)
    pause_started = models.DateTimeField(null=True, blank=True)
    
    # Dependencies (stored as JSON array of task IDs)
    dependencies = models.JSONField(default=list, blank=True)
    
    # Quality control
    quality_checklist = models.JSONField(default=dict, blank=True)
    quality_check_passed = models.BooleanField(null=True, blank=True)
    
    # Additional data
    required_skills = models.JSONField(default=dict, blank=True)
    required_equipment = models.JSONField(default=dict, blank=True)
    blocking_issues = models.TextField(blank=True)
    review_comments = models.TextField(blank=True)
    notes = models.TextField(blank=True)
    
    # Status history stored as JSON
    status_history = models.JSONField(default=list, blank=True)
    
    class Meta:
        db_table = 'tasks'
        indexes = [
            models.Index(fields=['case_number']),
            models.Index(fields=['status']),
            models.Index(fields=['assigned_to']),
            models.Index(fields=['priority']),
            models.Index(fields=['created_at']),
            models.Index(fields=['case_number', 'status']),
        ]
        ordering = ['-priority', 'created_at']
    
    def __str__(self):
        return f"Task #{self.task_id}: {self.title}"
    
    # Domain Model Conversion Methods
    @staticmethod
    def update_from_domain(task: domain_model.Task):
        """Update or create ORM model from domain model"""
        try:
            orm_task = Task.objects.get(task_id=task.task_id)
        except Task.DoesNotExist:
            orm_task = Task(task_id=task.task_id)
        
        # Basic fields
        orm_task.case_number = task.case_number
        orm_task.title = task.title
        orm_task.description = getattr(task, 'description', '')
        orm_task.status = task.status.value if hasattr(task.status, 'value') else task.status
        orm_task.priority = task.priority.value if hasattr(task.priority, 'value') else task.priority
        
        # Relationships
        orm_task.case_item_id = task.case_item_id
        orm_task.workflow_stage_id = task.workflow_stage_id
        orm_task.assigned_to = task.assigned_to
        
        # Progress
        orm_task.progress_percentage = task.progress_percentage
        
        # Time tracking - convert timedelta to seconds
        orm_task.estimated_duration_seconds = int(task.estimated_duration.total_seconds())
        if task.actual_duration:
            orm_task.actual_duration_seconds = int(task.actual_duration.total_seconds())
        orm_task.paused_time_seconds = int(task.paused_time.total_seconds())
        
        # Timestamps
        orm_task.actual_start_time = task.actual_start_time
        orm_task.actual_end_time = task.actual_end_time
        
        # Collections
        orm_task.dependencies = list(task.dependencies)
        
        # Quality
        orm_task.quality_check_passed = task.quality_check_passed
        
        # Text fields
        orm_task.blocking_issues = task.blocking_issues
        orm_task.review_comments = task.review_comments
        
        orm_task.save()
        return orm_task
    
    def to_domain(self) -> domain_model.Task:
        """Convert ORM model to domain model"""
        # Convert seconds back to timedelta
        estimated_duration = timedelta(seconds=self.estimated_duration_seconds)
        actual_duration = None
        if self.actual_duration_seconds:
            actual_duration = timedelta(seconds=self.actual_duration_seconds)
        paused_time = timedelta(seconds=self.paused_time_seconds)
        
        # Create domain task
        domain_task = domain_model.Task(
            task_id=self.task_id,
            case_number=self.case_number,
            title=self.title,
            estimated_duration=estimated_duration,
            assigned_to=self.assigned_to,
            case_item_id=self.case_item_id,
            workflow_stage_id=self.workflow_stage_id
        )
        
        # Set additional properties
        domain_task.description = self.description
        domain_task.status = domain_model.TaskStatus(self.status)
        domain_task.priority = domain_model.Priority(self.priority)
        domain_task.progress_percentage = self.progress_percentage
        
        domain_task.actual_duration = actual_duration
        domain_task.paused_time = paused_time
        
        domain_task.actual_start_time = self.actual_start_time
        domain_task.actual_end_time = self.actual_end_time
        
        domain_task.dependencies = set(self.dependencies)
        domain_task.quality_check_passed = self.quality_check_passed
        domain_task.blocking_issues = self.blocking_issues
        domain_task.review_comments = self.review_comments
        
        # Clear events
        domain_task.events = []
        
        return domain_task
    
    @staticmethod
    def update_tasks_from_domain(case: domain_model.Case):
        """Update all tasks for a case from domain model"""
        # Remove tasks that are no longer in the domain model
        existing_task_ids = set(Task.objects.filter(case_number=case.case_number).values_list('task_id', flat=True))
        domain_task_ids = set(task.task_id for task in case.tasks if task.task_id)
        
        # Delete removed tasks
        tasks_to_delete = existing_task_ids - domain_task_ids
        Task.objects.filter(task_id__in=tasks_to_delete).delete()
        
        # Update or create tasks
        for task in case.tasks:
            Task.update_from_domain(task)


class CaseItem(models.Model):
    """
    ORM model for CaseItem - ONLY database structure
    Business logic is in domain.model.CaseItem
    """
    
    STATUS_CHOICES = Task.STATUS_CHOICES  # Same as Task
    
    id = models.AutoField(primary_key=True)
    
    # Relationships
    case_number = models.IntegerField(db_index=True)
    item_id = models.IntegerField(db_index=True)
    unit_id = models.IntegerField()
    assigned_to = models.IntegerField(null=True, blank=True)
    
    # Item details
    quantity = models.PositiveIntegerField(validators=[MinValueValidator(1)])
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    notes = models.TextField(blank=True)
    
    # Time tracking (stored as seconds)
    estimated_time_seconds = models.BigIntegerField(default=3600)  # Default 1 hour
    actual_time_seconds = models.BigIntegerField(null=True, blank=True)
    
    # Metadata
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'case_items'
        unique_together = [['case_number', 'item_id']]
        indexes = [
            models.Index(fields=['case_number']),
            models.Index(fields=['item_id']),
            models.Index(fields=['status']),
            models.Index(fields=['assigned_to']),
        ]
    
    def __str__(self):
        return f"Case #{self.case_number} - Item #{self.item_id}"
    
    # Domain Model Conversion Methods
    @staticmethod
    def update_from_domain(case_item: domain_model.CaseItem):
        """Update or create ORM model from domain model"""
        try:
            orm_item = CaseItem.objects.get(case_number=case_item.case_number, item_id=case_item.item_id)
        except CaseItem.DoesNotExist:
            orm_item = CaseItem()
        
        # Basic fields
        orm_item.case_number = case_item.case_number
        orm_item.item_id = case_item.item_id
        orm_item.quantity = case_item.quantity
        orm_item.unit_id = case_item.unit_id
        orm_item.status = case_item.status.value if hasattr(case_item.status, 'value') else case_item.status
        orm_item.notes = case_item.notes
        
        # Time tracking - convert timedelta to seconds
        orm_item.estimated_time_seconds = int(case_item.estimated_time.total_seconds())
        if case_item.actual_time:
            orm_item.actual_time_seconds = int(case_item.actual_time.total_seconds())
        
        orm_item.save()
        return orm_item
    
    def to_domain(self) -> domain_model.CaseItem:
        """Convert ORM model to domain model"""
        # Convert seconds back to timedelta
        estimated_time = timedelta(seconds=self.estimated_time_seconds)
        actual_time = None
        if self.actual_time_seconds:
            actual_time = timedelta(seconds=self.actual_time_seconds)
        
        # Create domain case item
        domain_item = domain_model.CaseItem(
            case_number=self.case_number,
            item_id=self.item_id,
            quantity=self.quantity,
            unit_id=self.unit_id,
            estimated_time=estimated_time,
            notes=self.notes
        )
        
        # Set additional properties
        domain_item.status = domain_model.TaskStatus(self.status)
        domain_item.actual_time = actual_time
        
        # Clear events
        domain_item.events = []
        
        return domain_item
    
    @staticmethod
    def update_items_from_domain(case: domain_model.Case):
        """Update all case items for a case from domain model"""
        # Remove items that are no longer in the domain model
        existing_items = set(CaseItem.objects.filter(case_number=case.case_number).values_list('item_id', flat=True))
        domain_items = set(item.item_id for item in case.items)
        
        # Delete removed items
        items_to_delete = existing_items - domain_items
        CaseItem.objects.filter(case_number=case.case_number, item_id__in=items_to_delete).delete()
        
        # Update or create items
        for item in case.items:
            CaseItem.update_from_domain(item)


# Supporting models for domain concepts

class WorkflowTemplate(models.Model):
    """Workflow template for cases"""
    
    STATUS_CHOICES = [
        ('active', 'Active'),
        ('inactive', 'Inactive'),
        ('archived', 'Archived'),
    ]
    
    template_id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active')
    
    # Default settings
    default_duration_seconds = models.BigIntegerField()
    priority = models.IntegerField(choices=Case.PRIORITY_CHOICES, default=2)
    is_default = models.BooleanField(default=False)
    
    # Cost estimation
    estimated_cost_amount = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    estimated_cost_currency = models.CharField(max_length=3, default='EUR')
    
    # Metadata
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.IntegerField(null=True, blank=True)
    
    class Meta:
        db_table = 'workflow_templates'
        ordering = ['name']
    
    def __str__(self):
        return self.name


class WorkflowStage(models.Model):
    """Individual stages within a workflow template"""
    
    COMPLEXITY_CHOICES = [
        (1, 'Simple'),
        (2, 'Medium'),
        (3, 'Complex'),
    ]
    
    stage_id = models.AutoField(primary_key=True)
    workflow_template_id = models.IntegerField(db_index=True)
    
    # Stage details
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    order = models.PositiveSmallIntegerField()
    
    # Assignments
    department_id = models.IntegerField(db_index=True)
    
    # Time and complexity
    estimated_duration_seconds = models.BigIntegerField()
    complexity = models.IntegerField(choices=COMPLEXITY_CHOICES, default=1)
    is_critical = models.BooleanField(default=False)
    
    # Dependencies (JSON array of stage IDs)
    dependencies = models.JSONField(default=list, blank=True)
    
    # Requirements
    required_skills = models.JSONField(default=dict, blank=True)
    required_equipment = models.JSONField(default=dict, blank=True)
    quality_checklist = models.JSONField(default=dict, blank=True)
    
    # Metadata
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    notes = models.TextField(blank=True)
    
    class Meta:
        db_table = 'workflow_stages'
        unique_together = [['workflow_template_id', 'order']]
        indexes = [
            models.Index(fields=['workflow_template_id', 'order']),
            models.Index(fields=['department_id']),
            models.Index(fields=['is_critical']),
        ]
        ordering = ['workflow_template_id', 'order']
    
    def __str__(self):
        return f"Stage {self.order}: {self.name}"


class Department(models.Model):
    """Department/team for case assignment"""
    
    department_id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=50, unique=True)
    description = models.TextField(blank=True)
    
    # Capacity planning
    capacity = models.PositiveSmallIntegerField(help_text="Maximum concurrent tasks")
    capacity_hours = models.FloatField(default=160, help_text="Monthly capacity in hours")
    
    # Working hours
    working_hours_start = models.TimeField()
    working_hours_end = models.TimeField()
    
    # Management
    manager_id = models.IntegerField(null=True, blank=True)
    is_active = models.BooleanField(default=True)
    
    # Metadata
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'departments'
        ordering = ['name']
    
    def __str__(self):
        return self.name


# Event store for domain events
class DomainEventStore(models.Model):
    """Store for domain events - Event Sourcing pattern"""
    
    id = models.AutoField(primary_key=True)
    
    # Event identification
    aggregate_id = models.CharField(max_length=100, db_index=True)  # e.g., "case-1001"
    aggregate_type = models.CharField(max_length=50, db_index=True)  # e.g., "Case"
    event_type = models.CharField(max_length=100, db_index=True)     # e.g., "CaseCreated"
    event_version = models.PositiveIntegerField()
    
    # Event data
    event_data = models.JSONField()  # Serialized event data
    
    # Metadata
    occurred_at = models.DateTimeField(db_index=True)
    recorded_at = models.DateTimeField(default=timezone.now)
    user_id = models.IntegerField(null=True, blank=True)
    
    class Meta:
        db_table = 'domain_events'
        unique_together = [['aggregate_id', 'event_version']]
        indexes = [
            models.Index(fields=['aggregate_id', 'event_version']),
            models.Index(fields=['aggregate_type']),
            models.Index(fields=['event_type']),
            models.Index(fields=['occurred_at']),
            models.Index(fields=['recorded_at']),
        ]
        ordering = ['recorded_at']
    
    def __str__(self):
        return f"{self.event_type} for {self.aggregate_id}"


# Read models for CQRS pattern (denormalized views)
class CaseSummaryView(models.Model):
    """Denormalized view for case list display - CQRS read model"""
    
    case_number = models.IntegerField(primary_key=True)
    
    # Basic info
    dentist_name = models.CharField(max_length=100)
    patient_name = models.CharField(max_length=100, blank=True)
    status = models.CharField(max_length=30, db_index=True)
    priority = models.IntegerField()
    
    # Dates
    created_at = models.DateTimeField(db_index=True)
    deadline = models.DateTimeField(null=True, blank=True, db_index=True)
    estimated_completion = models.DateTimeField(null=True, blank=True)
    
    # Progress
    progress_percentage = models.DecimalField(max_digits=5, decimal_places=2)
    items_count = models.PositiveIntegerField(default=0)
    tasks_count = models.PositiveIntegerField(default=0)
    completed_tasks_count = models.PositiveIntegerField(default=0)
    
    # Flags for quick filtering
    is_overdue = models.BooleanField(default=False, db_index=True)
    is_urgent = models.BooleanField(default=False, db_index=True)
    requires_attention = models.BooleanField(default=False, db_index=True)
    
    # Workflow
    current_stage_name = models.CharField(max_length=100, blank=True)
    responsible_department_name = models.CharField(max_length=50, blank=True)
    
    # Last updated (for cache invalidation)
    last_updated = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'case_summary_view'
        indexes = [
            models.Index(fields=['status']),
            models.Index(fields=['created_at']),
            models.Index(fields=['deadline']),
            models.Index(fields=['is_overdue']),
            models.Index(fields=['is_urgent']),
            models.Index(fields=['requires_attention']),
            models.Index(fields=['dentist_name']),
        ]
    
    def __str__(self):
        return f"Case #{self.case_number} Summary"