# DENTAL LAB DDD - Django Forms
# Forms for input validation and user interaction

from django import forms
from django.core.exceptions import ValidationError
from datetime import datetime, timedelta
from decimal import Decimal


class CreateCaseForm(forms.Form):
    """Form for creating a new dental case"""
    
    PRIORITY_CHOICES = [
        (1, 'Low'),
        (2, 'Normal'),
        (3, 'High'),
        (4, 'Urgent'),
    ]
    
    dentist_id = forms.IntegerField(
        label='Dentist',
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    patient_id = forms.IntegerField(
        label='Patient',
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    deadline = forms.DateTimeField(
        label='Deadline',
        required=False,
        widget=forms.DateTimeInput(attrs={
            'class': 'form-control',
            'type': 'datetime-local'
        })
    )
    
    priority = forms.ChoiceField(
        choices=PRIORITY_CHOICES,
        initial=2,
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    notes = forms.CharField(
        label='Notes',
        required=False,
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 3,
            'placeholder': 'Any additional notes or instructions...'
        })
    )
    
    special_requirements = forms.CharField(
        label='Special Requirements',
        required=False,
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 2,
            'placeholder': 'Special requirements or considerations...'
        })
    )
    
    def clean_deadline(self):
        """Validate that deadline is in the future"""
        deadline = self.cleaned_data.get('deadline')
        if deadline and deadline <= datetime.now():
            raise ValidationError("Deadline must be in the future.")
        return deadline
    
    def clean_dentist_id(self):
        """Validate dentist ID"""
        dentist_id = self.cleaned_data.get('dentist_id')
        if dentist_id and dentist_id <= 0:
            raise ValidationError("Invalid dentist selection.")
        return dentist_id


class AddItemToCaseForm(forms.Form):
    """Form for adding an item to a case"""
    
    item_id = forms.IntegerField(
        label='Item',
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    quantity = forms.IntegerField(
        label='Quantity',
        min_value=1,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'min': '1'
        })
    )
    
    unit_id = forms.IntegerField(
        label='Unit',
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    estimated_time_hours = forms.DecimalField(
        label='Estimated Time (hours)',
        required=False,
        min_value=Decimal('0.1'),
        max_value=Decimal('999'),
        decimal_places=1,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'step': '0.5',
            'placeholder': 'e.g., 2.5'
        })
    )
    
    special_instructions = forms.CharField(
        label='Special Instructions',
        required=False,
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 2,
            'placeholder': 'Any special instructions for this item...'
        })
    )
    
    def clean_estimated_time_hours(self):
        """Convert hours to timedelta"""
        hours = self.cleaned_data.get('estimated_time_hours')
        if hours:
            return timedelta(hours=float(hours))
        return None
    
    def clean_quantity(self):
        """Validate quantity"""
        quantity = self.cleaned_data.get('quantity')
        if quantity and quantity <= 0:
            raise ValidationError("Quantity must be positive.")
        return quantity


class CreatePatientForm(forms.Form):
    """Form for registering a new patient"""
    
    dentist_id = forms.IntegerField(
        label='Dentist',
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    first_name = forms.CharField(
        label='First Name',
        max_length=50,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Enter first name'
        })
    )
    
    last_name = forms.CharField(
        label='Last Name',
        max_length=50,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Enter last name'
        })
    )
    
    phone_number = forms.CharField(
        label='Phone Number',
        max_length=15,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'e.g., +**********'
        })
    )
    
    email = forms.EmailField(
        label='Email',
        required=False,
        widget=forms.EmailInput(attrs={
            'class': 'form-control',
            'placeholder': '<EMAIL>'
        })
    )
    
    address = forms.CharField(
        label='Address',
        required=False,
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 2,
            'placeholder': 'Full address...'
        })
    )
    
    date_of_birth = forms.DateField(
        label='Date of Birth',
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        })
    )
    
    def clean_first_name(self):
        """Validate first name"""
        first_name = self.cleaned_data.get('first_name', '').strip()
        if not first_name:
            raise ValidationError("First name is required.")
        return first_name
    
    def clean_date_of_birth(self):
        """Validate date of birth"""
        date_of_birth = self.cleaned_data.get('date_of_birth')
        if date_of_birth and date_of_birth >= datetime.now().date():
            raise ValidationError("Date of birth must be in the past.")
        return date_of_birth


class CreateDentistForm(forms.Form):
    """Form for registering a new dentist"""
    
    first_name = forms.CharField(
        label='First Name',
        max_length=50,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Enter first name'
        })
    )
    
    last_name = forms.CharField(
        label='Last Name',
        max_length=50,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Enter last name'
        })
    )
    
    clinic_name = forms.CharField(
        label='Clinic Name',
        max_length=100,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Enter clinic name'
        })
    )
    
    phone_number = forms.CharField(
        label='Phone Number',
        max_length=15,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'e.g., +**********'
        })
    )
    
    email = forms.EmailField(
        label='Email',
        required=False,
        widget=forms.EmailInput(attrs={
            'class': 'form-control',
            'placeholder': '<EMAIL>'
        })
    )
    
    address = forms.CharField(
        label='Address',
        required=False,
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 2,
            'placeholder': 'Clinic address...'
        })
    )
    
    def clean_first_name(self):
        """Validate first name"""
        first_name = self.cleaned_data.get('first_name', '').strip()
        if not first_name:
            raise ValidationError("First name is required.")
        return first_name
    
    def clean_clinic_name(self):
        """Validate clinic name"""
        clinic_name = self.cleaned_data.get('clinic_name', '').strip()
        if not clinic_name:
            raise ValidationError("Clinic name is required.")
        return clinic_name


class CreateTaskForm(forms.Form):
    """Form for creating a new task"""
    
    PRIORITY_CHOICES = [
        (1, 'Low'),
        (2, 'Normal'),
        (3, 'High'),
        (4, 'Urgent'),
    ]
    
    title = forms.CharField(
        label='Task Title',
        max_length=200,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Enter task title'
        })
    )
    
    description = forms.CharField(
        label='Description',
        required=False,
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 3,
            'placeholder': 'Detailed description of the task...'
        })
    )
    
    estimated_duration_hours = forms.DecimalField(
        label='Estimated Duration (hours)',
        min_value=Decimal('0.1'),
        max_value=Decimal('999'),
        decimal_places=1,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'step': '0.5',
            'placeholder': 'e.g., 2.5'
        })
    )
    
    priority = forms.ChoiceField(
        choices=PRIORITY_CHOICES,
        initial=2,
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    assigned_to = forms.IntegerField(
        label='Assign To',
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    def clean_estimated_duration_hours(self):
        """Convert hours to timedelta"""
        hours = self.cleaned_data.get('estimated_duration_hours')
        if hours:
            return timedelta(hours=float(hours))
        return timedelta(hours=1)  # Default 1 hour
    
    def clean_title(self):
        """Validate title"""
        title = self.cleaned_data.get('title', '').strip()
        if not title:
            raise ValidationError("Task title is required.")
        return title


class UpdateCaseStatusForm(forms.Form):
    """Form for updating case status"""
    
    STATUS_CHOICES = [
        ('pending_acceptance', 'Pending Acceptance'),
        ('on_hold', 'On Hold'),
        ('in_progress', 'In Progress'),
        ('quality_check', 'Quality Check'),
        ('revision_needed', 'Revision Needed'),
        ('ready_to_ship', 'Ready to Ship'),
        ('shipped', 'Shipped'),
        ('delivered', 'Delivered'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
    ]
    
    status = forms.ChoiceField(
        choices=STATUS_CHOICES,
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    notes = forms.CharField(
        label='Update Notes',
        required=False,
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 2,
            'placeholder': 'Reason for status change...'
        })
    )


class SearchForm(forms.Form):
    """Generic search form"""
    
    search = forms.CharField(
        label='Search',
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Search...'
        })
    )
    
    status = forms.ChoiceField(
        label='Status',
        required=False,
        choices=[('', 'All Statuses')] + UpdateCaseStatusForm.STATUS_CHOICES,
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    dentist_id = forms.IntegerField(
        label='Dentist',
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    date_from = forms.DateField(
        label='From Date',
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        })
    )
    
    date_to = forms.DateField(
        label='To Date',
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        })
    )
    
    def clean(self):
        """Validate date range"""
        cleaned_data = super().clean()
        date_from = cleaned_data.get('date_from')
        date_to = cleaned_data.get('date_to')
        
        if date_from and date_to and date_from > date_to:
            raise ValidationError("From date must be before to date.")
        
        return cleaned_data


class UpdateDeadlineForm(forms.Form):
    """Form for updating case deadline"""
    
    new_deadline = forms.DateTimeField(
        label='New Deadline',
        widget=forms.DateTimeInput(attrs={
            'class': 'form-control',
            'type': 'datetime-local'
        })
    )
    
    reason = forms.CharField(
        label='Reason for Change',
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 2,
            'placeholder': 'Reason for deadline change...'
        })
    )
    
    notify_dentist = forms.BooleanField(
        label='Notify Dentist',
        required=False,
        initial=True,
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input'})
    )
    
    def clean_new_deadline(self):
        """Validate new deadline"""
        new_deadline = self.cleaned_data.get('new_deadline')
        if new_deadline and new_deadline <= datetime.now():
            raise ValidationError("New deadline must be in the future.")
        return new_deadline


class BulkActionForm(forms.Form):
    """Form for bulk actions on multiple items"""
    
    ACTION_CHOICES = [
        ('', 'Select Action'),
        ('update_status', 'Update Status'),
        ('assign_technician', 'Assign Technician'),
        ('set_priority', 'Set Priority'),
        ('export', 'Export'),
    ]
    
    action = forms.ChoiceField(
        choices=ACTION_CHOICES,
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    selected_items = forms.CharField(
        widget=forms.HiddenInput()
    )
    
    # Dynamic fields based on action
    new_status = forms.ChoiceField(
        choices=UpdateCaseStatusForm.STATUS_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    technician_id = forms.IntegerField(
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    priority = forms.ChoiceField(
        choices=CreateCaseForm.PRIORITY_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    def clean_selected_items(self):
        """Parse selected items"""
        items_str = self.cleaned_data.get('selected_items', '')
        if not items_str:
            raise ValidationError("No items selected.")
        
        try:
            import json
            items = json.loads(items_str)
            if not isinstance(items, list) or not items:
                raise ValidationError("No valid items selected.")
            return items
        except (json.JSONDecodeError, ValueError):
            raise ValidationError("Invalid item selection.")
    
    def clean(self):
        """Validate action-specific fields"""
        cleaned_data = super().clean()
        action = cleaned_data.get('action')
        
        if action == 'update_status' and not cleaned_data.get('new_status'):
            raise ValidationError("Status is required for status update action.")
        
        if action == 'assign_technician' and not cleaned_data.get('technician_id'):
            raise ValidationError("Technician is required for assignment action.")
        
        if action == 'set_priority' and not cleaned_data.get('priority'):
            raise ValidationError("Priority is required for priority update action.")
        
        return cleaned_data