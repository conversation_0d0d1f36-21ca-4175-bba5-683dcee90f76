# DENTAL LAB DDD - Domain Commands
# Commands represent user intentions - what should happen, not how

from datetime import datetime, timedelta
from dataclasses import dataclass
from typing import Optional, List, Dict, Any
from decimal import Decimal


# Case Commands
@dataclass(frozen=True)
class CreateCase:
    """Command to create a new case"""
    dentist_id: int
    patient_id: Optional[int] = None
    deadline: Optional[datetime] = None
    priority: int = 2  # Normal priority
    notes: str = ""
    special_requirements: str = ""
    workflow_template_id: Optional[int] = None


@dataclass(frozen=True)
class StartCase:
    """Command to start a case (move from pending to in-progress)"""
    case_number: int
    received_date_time: Optional[datetime] = None
    estimated_completion: Optional[datetime] = None


@dataclass(frozen=True)
class CompleteCase:
    """Command to complete a case"""
    case_number: int
    completion_notes: str = ""
    quality_approved_by: Optional[int] = None


@dataclass(frozen=True)
class AddItemToCase:
    """Command to add an item to a case"""
    case_number: int
    item_id: int
    quantity: int
    unit_id: int
    estimated_time: Optional[timedelta] = None
    special_instructions: str = ""


# Task Commands
@dataclass(frozen=True)
class AssignTask:
    """Command to assign a task to someone"""
    task_id: int
    assigned_to: int
    assignment_notes: str = ""
    deadline: Optional[datetime] = None


@dataclass(frozen=True)
class StartTask:
    """Command to start a task"""
    task_id: int
    start_notes: str = ""


@dataclass(frozen=True)
class CompleteTask:
    """Command to complete a task"""
    task_id: int
    completion_notes: str = ""
    actual_duration: Optional[timedelta] = None
    quality_check_passed: Optional[bool] = None
