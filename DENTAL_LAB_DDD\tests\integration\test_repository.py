# DENTAL LAB DDD - Integration Tests for Repository Layer
# Tests that verify repository implementations work correctly with database

import pytest
from datetime import datetime, timedelta
from decimal import Decimal
from django.test import TestCase

from dental_lab.domain import model, exceptions
from dental_lab.adapters import django_repositories, orm


@pytest.mark.django_db
class TestDjangoCaseRepository:
    """Integration tests for Django Case repository"""
    
    def setup_method(self):
        """Set up test data for each test"""
        self.repo = django_repositories.DjangoCaseRepository()
        
        # Create test data
        self.dentist_id = 100
        self.patient_id = 1
        self.case_number = 1001
        
        # Clean up any existing test data
        orm.Case.objects.filter(case_number=self.case_number).delete()
    
    def test_add_and_get_case(self):
        """Test adding and retrieving a case"""
        # Create a domain case
        deadline = datetime.now() + timedelta(days=7)
        case = model.Case(
            case_number=self.case_number,
            dentist_id=self.dentist_id,
            patient_id=self.patient_id,
            deadline=deadline
        )
        
        # Add item to case
        case.add_item(item_id=50, quantity=2, unit_id=1, estimated_time=timedelta(hours=3))
        
        # Save to repository
        self.repo.add(case)
        
        # Retrieve from repository
        retrieved_case = self.repo.get_by_case_number(self.case_number)
        
        # Verify the case was saved and retrieved correctly
        assert retrieved_case is not None
        assert retrieved_case.case_number == self.case_number
        assert retrieved_case.dentist_id == self.dentist_id
        assert retrieved_case.patient_id == self.patient_id
        assert retrieved_case.status == model.CaseStatus.PENDING_ACCEPTANCE
        assert retrieved_case.deadline == deadline
        
        # Verify items were saved
        assert len(retrieved_case.items) == 1
        assert retrieved_case.items[0].item_id == 50
        assert retrieved_case.items[0].quantity == 2
        assert retrieved_case.items[0].unit_id == 1
    
    def test_update_case(self):
        """Test updating an existing case"""
        # Create and save initial case
        case = model.Case(
            case_number=self.case_number,
            dentist_id=self.dentist_id,
            patient_id=self.patient_id
        )
        self.repo.add(case)
        
        # Modify the case
        case.status = model.CaseStatus.IN_PROGRESS
        case.priority = model.Priority.HIGH
        case.notes = "Updated notes"
        
        # Update in repository
        self.repo.update(case)
        
        # Retrieve and verify changes
        updated_case = self.repo.get_by_case_number(self.case_number)
        assert updated_case.status == model.CaseStatus.IN_PROGRESS
        assert updated_case.priority == model.Priority.HIGH
        assert updated_case.notes == "Updated notes"
    
    def test_list_active_cases(self):
        """Test listing active cases"""
        # Create test cases with different statuses
        active_case = model.Case(1001, self.dentist_id, self.patient_id)
        completed_case = model.Case(1002, self.dentist_id, self.patient_id)
        completed_case.status = model.CaseStatus.COMPLETED
        
        # Save cases
        self.repo.add(active_case)
        self.repo.add(completed_case)
        
        # List active cases
        active_cases = self.repo.list_active_cases()
        
        # Verify only active case is returned
        active_case_numbers = [case.case_number for case in active_cases]
        assert 1001 in active_case_numbers
        assert 1002 not in active_case_numbers
    
    def test_list_overdue_cases(self):
        """Test listing overdue cases"""
        # Create overdue case
        past_deadline = datetime.now() - timedelta(days=1)
        overdue_case = model.Case(
            1001, self.dentist_id, self.patient_id, deadline=past_deadline
        )
        
        # Create case with future deadline
        future_deadline = datetime.now() + timedelta(days=1)
        future_case = model.Case(
            1002, self.dentist_id, self.patient_id, deadline=future_deadline
        )
        
        # Save cases
        self.repo.add(overdue_case)
        self.repo.add(future_case)
        
        # List overdue cases
        overdue_cases = self.repo.list_overdue_cases()
        
        # Verify only overdue case is returned
        overdue_case_numbers = [case.case_number for case in overdue_cases]
        assert 1001 in overdue_case_numbers
        assert 1002 not in overdue_case_numbers
    
    def test_list_cases_by_dentist(self):
        """Test listing cases by dentist"""
        # Create cases for different dentists
        dentist_100_case = model.Case(1001, 100, self.patient_id)
        dentist_200_case = model.Case(1002, 200, self.patient_id)
        
        # Save cases
        self.repo.add(dentist_100_case)
        self.repo.add(dentist_200_case)
        
        # List cases for dentist 100
        dentist_100_cases = self.repo.list_cases_by_dentist(100)
        
        # Verify only dentist 100's cases are returned
        case_numbers = [case.case_number for case in dentist_100_cases]
        assert 1001 in case_numbers
        assert 1002 not in case_numbers
    
    def test_search_cases(self):
        """Test searching cases with criteria"""
        # Create test cases
        high_priority_case = model.Case(1001, self.dentist_id, self.patient_id)
        high_priority_case.priority = model.Priority.HIGH
        
        low_priority_case = model.Case(1002, self.dentist_id, self.patient_id)
        low_priority_case.priority = model.Priority.LOW
        
        # Save cases
        self.repo.add(high_priority_case)
        self.repo.add(low_priority_case)
        
        # Search for high priority cases
        search_results = self.repo.search_cases({'priority': 3})  # High priority = 3
        
        # Verify search results
        result_case_numbers = [case.case_number for case in search_results]
        assert 1001 in result_case_numbers
        assert 1002 not in result_case_numbers
    
    def test_get_next_case_number(self):
        """Test generating next case number"""
        # Test with no existing cases
        next_number = self.repo.get_next_case_number()
        assert next_number == 1000  # Starting number
        
        # Add a case
        case = model.Case(1500, self.dentist_id, self.patient_id)
        self.repo.add(case)
        
        # Test with existing case
        next_number = self.repo.get_next_case_number()
        assert next_number == 1501
    
    def test_count_cases_by_status(self):
        """Test counting cases by status"""
        # Create cases with different statuses
        pending_case = model.Case(1001, self.dentist_id, self.patient_id)
        in_progress_case = model.Case(1002, self.dentist_id, self.patient_id)
        in_progress_case.status = model.CaseStatus.IN_PROGRESS
        
        # Save cases
        self.repo.add(pending_case)
        self.repo.add(in_progress_case)
        
        # Count cases by status
        status_counts = self.repo.count_cases_by_status()
        
        # Verify counts
        assert status_counts.get('pending_acceptance', 0) >= 1
        assert status_counts.get('in_progress', 0) >= 1
    
    def test_case_not_found_exception(self):
        """Test that appropriate exception is raised for non-existent case"""
        with pytest.raises(exceptions.CaseNotFound):
            non_existent_case = model.Case(9999, self.dentist_id, self.patient_id)
            self.repo.update(non_existent_case)
    
    def test_case_with_money_value_object(self):
        """Test saving and retrieving case with Money value object"""
        case = model.Case(self.case_number, self.dentist_id, self.patient_id)
        case.cost_estimate = model.Money(Decimal('150.50'), 'EUR')
        
        # Save and retrieve
        self.repo.add(case)
        retrieved_case = self.repo.get_by_case_number(self.case_number)
        
        # Verify money value object
        assert retrieved_case.cost_estimate is not None
        assert retrieved_case.cost_estimate.amount == Decimal('150.50')
        assert retrieved_case.cost_estimate.currency == 'EUR'


@pytest.mark.django_db
class TestDjangoPatientRepository:
    """Integration tests for Django Patient repository"""
    
    def setup_method(self):
        """Set up test data for each test"""
        self.repo = django_repositories.DjangoPatientRepository()
        self.dentist_id = 100
        self.patient_id = 1
        
        # Clean up any existing test data
        orm.Patient.objects.filter(patient_id=self.patient_id).delete()
    
    def test_add_and_get_patient(self):
        """Test adding and retrieving a patient"""
        # Create domain patient
        name = model.PersonName("John", "Doe")
        contact = model.ContactInfo(
            phone="************",
            email="<EMAIL>",
            address="123 Main St"
        )
        
        patient = model.Patient(
            patient_id=self.patient_id,
            name=name,
            dentist_id=self.dentist_id,
            contact=contact,
            date_of_birth=datetime(1990, 5, 15)
        )
        
        # Save to repository
        self.repo.add(patient)
        
        # Retrieve from repository
        retrieved_patient = self.repo.get_by_patient_id(self.patient_id)
        
        # Verify patient data
        assert retrieved_patient is not None
        assert retrieved_patient.patient_id == self.patient_id
        assert retrieved_patient.name.full_name == "John Doe"
        assert retrieved_patient.dentist_id == self.dentist_id
        assert retrieved_patient.contact.phone == "************"
        assert retrieved_patient.contact.email == "<EMAIL>"
        assert retrieved_patient.date_of_birth == datetime(1990, 5, 15)
    
    def test_update_patient(self):
        """Test updating patient information"""
        # Create and save patient
        name = model.PersonName("Jane", "Smith")
        patient = model.Patient(self.patient_id, name, self.dentist_id)
        self.repo.add(patient)
        
        # Update patient contact
        new_contact = model.ContactInfo(
            phone="************",
            email="<EMAIL>"
        )
        patient.contact = new_contact
        
        # Update in repository
        self.repo.update(patient)
        
        # Retrieve and verify
        updated_patient = self.repo.get_by_patient_id(self.patient_id)
        assert updated_patient.contact.phone == "************"
        assert updated_patient.contact.email == "<EMAIL>"
    
    def test_list_patients_by_dentist(self):
        """Test listing patients by dentist"""
        # Create patients for different dentists
        name1 = model.PersonName("Patient", "One")
        name2 = model.PersonName("Patient", "Two")
        
        patient_dentist_100 = model.Patient(1, name1, 100)
        patient_dentist_200 = model.Patient(2, name2, 200)
        
        # Save patients
        self.repo.add(patient_dentist_100)
        self.repo.add(patient_dentist_200)
        
        # List patients for dentist 100
        dentist_100_patients = self.repo.list_patients_by_dentist(100)
        
        # Verify results
        patient_ids = [p.patient_id for p in dentist_100_patients]
        assert 1 in patient_ids
        assert 2 not in patient_ids
    
    def test_search_patients(self):
        """Test searching patients by name"""
        # Create test patients
        john_name = model.PersonName("John", "Doe")
        jane_name = model.PersonName("Jane", "Smith")
        
        john_patient = model.Patient(1, john_name, self.dentist_id)
        jane_patient = model.Patient(2, jane_name, self.dentist_id)
        
        # Save patients
        self.repo.add(john_patient)
        self.repo.add(jane_patient)
        
        # Search for "John"
        search_results = self.repo.search_patients("John", self.dentist_id)
        
        # Verify search results
        assert len(search_results) == 1
        assert search_results[0].name.first_name == "John"
    
    def test_patient_not_found_exception(self):
        """Test exception handling for non-existent patient"""
        with pytest.raises(exceptions.PatientNotFound):
            non_existent_patient = model.Patient(
                9999, 
                model.PersonName("Test", "Patient"), 
                self.dentist_id
            )
            self.repo.update(non_existent_patient)


@pytest.mark.django_db 
class TestDjangoDentistRepository:
    """Integration tests for Django Dentist repository"""
    
    def setup_method(self):
        """Set up test data for each test"""
        self.repo = django_repositories.DjangoDentistRepository()
        self.dentist_id = 100
        
        # Clean up any existing test data
        orm.Dentist.objects.filter(dentist_id=self.dentist_id).delete()
    
    def test_add_and_get_dentist(self):
        """Test adding and retrieving a dentist"""
        # Create domain dentist
        name = model.PersonName("Dr. Sarah", "Wilson")
        contact = model.ContactInfo(
            phone="555-0123",
            email="<EMAIL>",
            address="456 Oak St"
        )
        
        dentist = model.Dentist(
            dentist_id=self.dentist_id,
            name=name,
            clinic_name="Smile Dental Clinic",
            contact=contact,
            user_id=500
        )
        
        # Save to repository
        self.repo.add(dentist)
        
        # Retrieve from repository
        retrieved_dentist = self.repo.get_by_dentist_id(self.dentist_id)
        
        # Verify dentist data
        assert retrieved_dentist is not None
        assert retrieved_dentist.dentist_id == self.dentist_id
        assert retrieved_dentist.name.full_name == "Dr. Sarah Wilson"
        assert retrieved_dentist.clinic_name == "Smile Dental Clinic"
        assert retrieved_dentist.contact.phone == "555-0123"
        assert retrieved_dentist.user_id == 500
    
    def test_get_by_user_id(self):
        """Test retrieving dentist by user ID"""
        # Create and save dentist
        name = model.PersonName("Dr. John", "Smith")
        dentist = model.Dentist(
            self.dentist_id, name, "Test Clinic", 
            model.ContactInfo(), user_id=600
        )
        self.repo.add(dentist)
        
        # Retrieve by user ID
        retrieved_dentist = self.repo.get_by_user_id(600)
        
        # Verify
        assert retrieved_dentist is not None
        assert retrieved_dentist.dentist_id == self.dentist_id
        assert retrieved_dentist.user_id == 600
    
    def test_search_dentists(self):
        """Test searching dentists by name or clinic"""
        # Create test dentists
        name1 = model.PersonName("Dr. Alice", "Johnson")
        name2 = model.PersonName("Dr. Bob", "Brown")
        
        alice_dentist = model.Dentist(
            101, name1, "Family Dental", model.ContactInfo()
        )
        bob_dentist = model.Dentist(
            102, name2, "Orthodontic Center", model.ContactInfo()
        )
        
        # Save dentists
        self.repo.add(alice_dentist)
        self.repo.add(bob_dentist)
        
        # Search by name
        name_search = self.repo.search_dentists("Alice")
        assert len(name_search) == 1
        assert name_search[0].name.first_name == "Dr. Alice"
        
        # Search by clinic
        clinic_search = self.repo.search_dentists("Family")
        assert len(clinic_search) == 1
        assert clinic_search[0].clinic_name == "Family Dental"
    
    def test_soft_delete_dentist(self):
        """Test soft delete (mark as inactive)"""
        # Create and save dentist
        name = model.PersonName("Dr. Test", "Dentist")
        dentist = model.Dentist(self.dentist_id, name, "Test Clinic", model.ContactInfo())
        self.repo.add(dentist)
        
        # Verify dentist exists in active list
        active_dentists = self.repo.list_all_dentists()
        dentist_ids = [d.dentist_id for d in active_dentists]
        assert self.dentist_id in dentist_ids
        
        # Soft delete dentist
        self.repo.delete(self.dentist_id)
        
        # Verify dentist is no longer in active list
        active_dentists_after = self.repo.list_all_dentists()
        dentist_ids_after = [d.dentist_id for d in active_dentists_after]
        assert self.dentist_id not in dentist_ids_after


@pytest.mark.django_db
class TestDjangoTaskRepository:
    """Integration tests for Django Task repository"""
    
    def setup_method(self):
        """Set up test data for each test"""
        self.repo = django_repositories.DjangoTaskRepository()
        self.case_number = 1001
        self.task_id = 1
        
        # Clean up any existing test data
        orm.Task.objects.filter(task_id=self.task_id).delete()
    
    def test_add_and_get_task(self):
        """Test adding and retrieving a task"""
        # Create domain task
        task = model.Task(
            task_id=self.task_id,
            case_number=self.case_number,
            title="Polish crown",
            estimated_duration=timedelta(hours=2)
        )
        
        # Save to repository
        self.repo.add(task)
        
        # Retrieve from repository
        retrieved_task = self.repo.get_by_task_id(self.task_id)
        
        # Verify task data
        assert retrieved_task is not None
        assert retrieved_task.task_id == self.task_id
        assert retrieved_task.case_number == self.case_number
        assert retrieved_task.title == "Polish crown"
        assert retrieved_task.estimated_duration == timedelta(hours=2)
        assert retrieved_task.status == model.TaskStatus.PENDING
    
    def test_task_lifecycle(self):
        """Test complete task lifecycle through repository"""
        # Create and save task
        task = model.Task(
            self.task_id, self.case_number, "Test task", timedelta(hours=1)
        )
        self.repo.add(task)
        
        # Start task
        task.start_task()
        self.repo.update(task)
        
        # Retrieve and verify
        updated_task = self.repo.get_by_task_id(self.task_id)
        assert updated_task.status == model.TaskStatus.IN_PROGRESS
        assert updated_task.actual_start_time is not None
        
        # Complete task
        task.complete_task()
        self.repo.update(task)
        
        # Retrieve and verify completion
        completed_task = self.repo.get_by_task_id(self.task_id)
        assert completed_task.status == model.TaskStatus.COMPLETED
        assert completed_task.actual_end_time is not None
    
    def test_list_tasks_by_case(self):
        """Test listing tasks for a specific case"""
        # Create tasks for different cases
        task_case_1001 = model.Task(1, 1001, "Task 1", timedelta(hours=1))
        task_case_1002 = model.Task(2, 1002, "Task 2", timedelta(hours=1))
        
        # Save tasks
        self.repo.add(task_case_1001)
        self.repo.add(task_case_1002)
        
        # List tasks for case 1001
        case_1001_tasks = self.repo.list_tasks_by_case(1001)
        
        # Verify results
        task_ids = [t.task_id for t in case_1001_tasks]
        assert 1 in task_ids
        assert 2 not in task_ids
    
    def test_list_tasks_by_status(self):
        """Test listing tasks by status"""
        # Create tasks with different statuses
        pending_task = model.Task(1, self.case_number, "Pending task", timedelta(hours=1))
        completed_task = model.Task(2, self.case_number, "Completed task", timedelta(hours=1))
        completed_task.status = model.TaskStatus.COMPLETED
        
        # Save tasks
        self.repo.add(pending_task)
        self.repo.add(completed_task)
        
        # List pending tasks
        pending_tasks = self.repo.list_tasks_by_status(model.TaskStatus.PENDING)
        
        # Verify results
        pending_task_ids = [t.task_id for t in pending_tasks]
        assert 1 in pending_task_ids
        assert 2 not in pending_task_ids


class TestRepositoryIntegration:
    """Integration tests that test multiple repositories working together"""
    
    @pytest.mark.django_db
    def test_case_with_patient_and_dentist(self):
        """Test creating a case with patient and dentist relationships"""
        # Create repositories
        case_repo = django_repositories.DjangoCaseRepository()
        patient_repo = django_repositories.DjangoPatientRepository()
        dentist_repo = django_repositories.DjangoDentistRepository()
        
        # Create dentist
        dentist_name = model.PersonName("Dr. Test", "Dentist")
        dentist = model.Dentist(100, dentist_name, "Test Clinic", model.ContactInfo())
        dentist_repo.add(dentist)
        
        # Create patient
        patient_name = model.PersonName("Test", "Patient")
        patient = model.Patient(1, patient_name, 100)
        patient_repo.add(patient)
        
        # Create case linking them
        case = model.Case(1001, 100, 1)
        case_repo.add(case)
        
        # Retrieve and verify relationships
        retrieved_case = case_repo.get_by_case_number(1001)
        retrieved_dentist = dentist_repo.get_by_dentist_id(100)
        retrieved_patient = patient_repo.get_by_patient_id(1)
        
        assert retrieved_case.dentist_id == retrieved_dentist.dentist_id
        assert retrieved_case.patient_id == retrieved_patient.patient_id
    
    @pytest.mark.django_db
    def test_repository_factory(self):
        """Test repository factory creates correct instances"""
        factory = django_repositories.DjangoRepositoryFactory()
        
        # Test factory creates correct repository types
        case_repo = factory.create_case_repository()
        patient_repo = factory.create_patient_repository()
        dentist_repo = factory.create_dentist_repository()
        task_repo = factory.create_task_repository()
        case_item_repo = factory.create_case_item_repository()
        
        assert isinstance(case_repo, django_repositories.DjangoCaseRepository)
        assert isinstance(patient_repo, django_repositories.DjangoPatientRepository)
        assert isinstance(dentist_repo, django_repositories.DjangoDentistRepository)
        assert isinstance(task_repo, django_repositories.DjangoTaskRepository)
        assert isinstance(case_item_repo, django_repositories.DjangoCaseItemRepository)