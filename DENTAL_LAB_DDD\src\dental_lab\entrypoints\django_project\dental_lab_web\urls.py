# DENTAL LAB DDD - Django Main URL Configuration

from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static

urlpatterns = [
    path('admin/', admin.site.urls),
    path('api/', include('web.api_urls')),  # API endpoints
    path('', include('web.urls')),  # Web interface
]

# Serve media files in development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)

# Customize admin site
admin.site.site_header = "Dental Lab DDD Administration"
admin.site.site_title = "Dental Lab DDD Admin"
admin.site.index_title = "Welcome to Dental Lab DDD Administration"