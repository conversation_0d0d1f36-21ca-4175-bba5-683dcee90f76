# DENTAL LAB DDD - Repository Interfaces
# Abstract repository patterns - define contracts without implementation

import abc
from typing import List, Optional, Dict, Any
from datetime import datetime

from dental_lab.domain import model


class AbstractRepository(abc.ABC):
    """Base repository interface"""
    
    @abc.abstractmethod
    def add(self, entity) -> None:
        """Add an entity to the repository"""
        raise NotImplementedError
    
    @abc.abstractmethod
    def get(self, entity_id) -> Optional[object]:
        """Get an entity by its ID"""
        raise NotImplementedError
    
    @abc.abstractmethod
    def update(self, entity) -> None:
        """Update an existing entity"""
        raise NotImplementedError
    
    @abc.abstractmethod
    def delete(self, entity_id) -> None:
        """Delete an entity by its ID"""
        raise NotImplementedError


class AbstractCaseRepository(AbstractRepository):
    """Repository interface for Case aggregate root"""
    
    @abc.abstractmethod
    def get_by_case_number(self, case_number: int) -> Optional[model.Case]:
        """Get a case by its case number"""
        raise NotImplementedError
    
    @abc.abstractmethod
    def list_active_cases(self) -> List[model.Case]:
        """Get all active (non-completed, non-cancelled) cases"""
        raise NotImplementedError
    
    @abc.abstractmethod
    def list_overdue_cases(self) -> List[model.Case]:
        """Get all overdue cases"""
        raise NotImplementedError
    
    @abc.abstractmethod
    def list_cases_by_dentist(self, dentist_id: int) -> List[model.Case]:
        """Get all cases for a specific dentist"""
        raise NotImplementedError
    
    @abc.abstractmethod
    def list_cases_by_status(self, status: model.CaseStatus) -> List[model.Case]:
        """Get all cases with specific status"""
        raise NotImplementedError
    
    @abc.abstractmethod
    def list_cases_by_department(self, department_id: int) -> List[model.Case]:
        """Get all cases assigned to a specific department"""
        raise NotImplementedError
    
    @abc.abstractmethod
    def list_cases_due_soon(self, days: int = 3) -> List[model.Case]:
        """Get cases due within specified days"""
        raise NotImplementedError
    
    @abc.abstractmethod
    def search_cases(self, criteria: Dict[str, Any]) -> List[model.Case]:
        """Search cases by various criteria"""
        raise NotImplementedError
    
    @abc.abstractmethod
    def get_next_case_number(self) -> int:
        """Generate the next available case number"""
        raise NotImplementedError
    
    @abc.abstractmethod
    def count_cases_by_status(self) -> Dict[str, int]:
        """Get count of cases grouped by status"""
        raise NotImplementedError


class AbstractPatientRepository(AbstractRepository):
    """Repository interface for Patient entity"""
    
    @abc.abstractmethod
    def get_by_patient_id(self, patient_id: int) -> Optional[model.Patient]:
        """Get a patient by patient ID"""
        raise NotImplementedError
    
    @abc.abstractmethod
    def list_patients_by_dentist(self, dentist_id: int) -> List[model.Patient]:
        """Get all patients for a specific dentist"""
        raise NotImplementedError
    
    @abc.abstractmethod
    def search_patients(self, name_query: str, dentist_id: Optional[int] = None) -> List[model.Patient]:
        """Search patients by name"""
        raise NotImplementedError
    
    @abc.abstractmethod
    def get_patient_with_cases(self, patient_id: int) -> Optional[model.Patient]:
        """Get patient with their associated cases loaded"""
        raise NotImplementedError
    
    @abc.abstractmethod
    def list_patients_with_active_cases(self) -> List[model.Patient]:
        """Get patients who currently have active cases"""
        raise NotImplementedError


class AbstractDentistRepository(AbstractRepository):
    """Repository interface for Dentist entity"""
    
    @abc.abstractmethod
    def get_by_dentist_id(self, dentist_id: int) -> Optional[model.Dentist]:
        """Get a dentist by dentist ID"""
        raise NotImplementedError
    
    @abc.abstractmethod
    def get_by_user_id(self, user_id: int) -> Optional[model.Dentist]:
        """Get a dentist by their user account ID"""
        raise NotImplementedError
    
    @abc.abstractmethod
    def list_all_dentists(self) -> List[model.Dentist]:
        """Get all registered dentists"""
        raise NotImplementedError
    
    @abc.abstractmethod
    def search_dentists(self, name_query: str) -> List[model.Dentist]:
        """Search dentists by name or clinic"""
        raise NotImplementedError
    
    @abc.abstractmethod
    def get_dentist_with_patients(self, dentist_id: int) -> Optional[model.Dentist]:
        """Get dentist with their patients loaded"""
        raise NotImplementedError
    
    @abc.abstractmethod
    def list_dentists_with_active_cases(self) -> List[model.Dentist]:
        """Get dentists who currently have active cases"""
        raise NotImplementedError


class AbstractTaskRepository(AbstractRepository):
    """Repository interface for Task entity"""
    
    @abc.abstractmethod
    def get_by_task_id(self, task_id: int) -> Optional[model.Task]:
        """Get a task by task ID"""
        raise NotImplementedError
    
    @abc.abstractmethod
    def list_tasks_by_case(self, case_number: int) -> List[model.Task]:
        """Get all tasks for a specific case"""
        raise NotImplementedError
    
    @abc.abstractmethod
    def list_tasks_by_assignee(self, assignee_id: int) -> List[model.Task]:
        """Get all tasks assigned to a specific person"""
        raise NotImplementedError
    
    @abc.abstractmethod
    def list_tasks_by_status(self, status: model.TaskStatus) -> List[model.Task]:
        """Get all tasks with specific status"""
        raise NotImplementedError
    
    @abc.abstractmethod
    def list_overdue_tasks(self) -> List[model.Task]:
        """Get all overdue tasks"""
        raise NotImplementedError
    
    @abc.abstractmethod
    def list_pending_tasks(self, assignee_id: Optional[int] = None) -> List[model.Task]:
        """Get pending tasks, optionally filtered by assignee"""
        raise NotImplementedError
    
    @abc.abstractmethod
    def list_blocked_tasks(self) -> List[model.Task]:
        """Get all blocked tasks"""
        raise NotImplementedError
    
    @abc.abstractmethod
    def list_tasks_by_workflow_stage(self, stage_id: int) -> List[model.Task]:
        """Get tasks by workflow stage"""
        raise NotImplementedError
    
    @abc.abstractmethod
    def get_next_task_id(self) -> int:
        """Generate the next available task ID"""
        raise NotImplementedError


class AbstractCaseItemRepository(AbstractRepository):
    """Repository interface for CaseItem entity"""
    
    @abc.abstractmethod
    def list_items_by_case(self, case_number: int) -> List[model.CaseItem]:
        """Get all items for a specific case"""
        raise NotImplementedError
    
    @abc.abstractmethod
    def get_case_item(self, case_number: int, item_id: int) -> Optional[model.CaseItem]:
        """Get a specific case item"""
        raise NotImplementedError
    
    @abc.abstractmethod
    def list_items_by_status(self, status: model.TaskStatus) -> List[model.CaseItem]:
        """Get case items by production status"""
        raise NotImplementedError
    
    @abc.abstractmethod
    def list_items_in_production(self) -> List[model.CaseItem]:
        """Get all items currently in production"""
        raise NotImplementedError
    
    @abc.abstractmethod
    def list_delayed_items(self) -> List[model.CaseItem]:
        """Get items that are delayed in production"""
        raise NotImplementedError


# Specialized query interfaces for complex business queries
class AbstractCaseAnalyticsRepository(abc.ABC):
    """Repository for case analytics and reporting"""
    
    @abc.abstractmethod
    def get_case_statistics(self, date_from: datetime, date_to: datetime) -> Dict[str, Any]:
        """Get case statistics for a date range"""
        raise NotImplementedError
    
    @abc.abstractmethod
    def get_dentist_case_summary(self, dentist_id: int, date_from: datetime, date_to: datetime) -> Dict[str, Any]:
        """Get case summary for a specific dentist"""
        raise NotImplementedError
    
    @abc.abstractmethod
    def get_production_metrics(self, date_from: datetime, date_to: datetime) -> Dict[str, Any]:
        """Get production efficiency metrics"""
        raise NotImplementedError
    
    @abc.abstractmethod
    def get_quality_metrics(self, date_from: datetime, date_to: datetime) -> Dict[str, Any]:
        """Get quality control metrics"""
        raise NotImplementedError
    
    @abc.abstractmethod
    def get_department_workload(self, department_id: Optional[int] = None) -> Dict[str, Any]:
        """Get current workload by department"""
        raise NotImplementedError


class AbstractWorkflowRepository(abc.ABC):
    """Repository for workflow-related queries"""
    
    @abc.abstractmethod
    def get_workflow_template(self, template_id: int) -> Optional[Dict[str, Any]]:
        """Get workflow template details"""
        raise NotImplementedError
    
    @abc.abstractmethod
    def get_workflow_stage(self, stage_id: int) -> Optional[Dict[str, Any]]:
        """Get workflow stage details"""
        raise NotImplementedError
    
    @abc.abstractmethod
    def list_workflow_templates(self) -> List[Dict[str, Any]]:
        """Get all available workflow templates"""
        raise NotImplementedError
    
    @abc.abstractmethod
    def get_case_workflow_progress(self, case_number: int) -> Dict[str, Any]:
        """Get workflow progress for a case"""
        raise NotImplementedError


# Event store interface for domain events
class AbstractEventStore(abc.ABC):
    """Repository for storing and retrieving domain events"""
    
    @abc.abstractmethod
    def save_events(self, aggregate_id: str, events: List[Any], expected_version: int) -> None:
        """Save domain events for an aggregate"""
        raise NotImplementedError
    
    @abc.abstractmethod
    def get_events(self, aggregate_id: str, from_version: int = 0) -> List[Any]:
        """Get all events for an aggregate from a specific version"""
        raise NotImplementedError
    
    @abc.abstractmethod
    def get_all_events(self, from_timestamp: datetime = None) -> List[Any]:
        """Get all events, optionally from a specific timestamp"""
        raise NotImplementedError
    
    @abc.abstractmethod
    def get_events_by_type(self, event_type: str, from_timestamp: datetime = None) -> List[Any]:
        """Get events of a specific type"""
        raise NotImplementedError


# Read model repositories for CQRS pattern
class AbstractCaseReadModelRepository(abc.ABC):
    """Read-only repository for case projections (CQRS pattern)"""
    
    @abc.abstractmethod
    def get_case_summary(self, case_number: int) -> Optional[Dict[str, Any]]:
        """Get case summary for display"""
        raise NotImplementedError
    
    @abc.abstractmethod
    def get_case_timeline(self, case_number: int) -> List[Dict[str, Any]]:
        """Get case timeline events"""
        raise NotImplementedError
    
    @abc.abstractmethod
    def get_dashboard_data(self, user_id: int, user_role: str) -> Dict[str, Any]:
        """Get dashboard data based on user role"""
        raise NotImplementedError
    
    @abc.abstractmethod
    def get_case_list_view(self, filters: Dict[str, Any], page: int = 1, page_size: int = 50) -> Dict[str, Any]:
        """Get paginated case list with filters"""
        raise NotImplementedError


class AbstractDashboardRepository(abc.ABC):
    """Repository for dashboard data and metrics"""
    
    @abc.abstractmethod
    def get_key_metrics(self) -> Dict[str, Any]:
        """Get key business metrics for dashboard"""
        raise NotImplementedError
    
    @abc.abstractmethod
    def get_recent_activity(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent activity feed"""
        raise NotImplementedError
    
    @abc.abstractmethod
    def get_alerts_and_notifications(self, user_id: int) -> List[Dict[str, Any]]:
        """Get alerts and notifications for a user"""
        raise NotImplementedError
    
    @abc.abstractmethod
    def get_workload_distribution(self) -> Dict[str, Any]:
        """Get current workload distribution across departments/technicians"""
        raise NotImplementedError


# Repository factory pattern
class AbstractRepositoryFactory(abc.ABC):
    """Factory for creating repository instances"""
    
    @abc.abstractmethod
    def create_case_repository(self) -> AbstractCaseRepository:
        """Create a case repository instance"""
        raise NotImplementedError
    
    @abc.abstractmethod
    def create_patient_repository(self) -> AbstractPatientRepository:
        """Create a patient repository instance"""
        raise NotImplementedError
    
    @abc.abstractmethod
    def create_dentist_repository(self) -> AbstractDentistRepository:
        """Create a dentist repository instance"""
        raise NotImplementedError
    
    @abc.abstractmethod
    def create_task_repository(self) -> AbstractTaskRepository:
        """Create a task repository instance"""
        raise NotImplementedError
    
    @abc.abstractmethod
    def create_case_item_repository(self) -> AbstractCaseItemRepository:
        """Create a case item repository instance"""
        raise NotImplementedError
    
    @abc.abstractmethod
    def create_analytics_repository(self) -> AbstractCaseAnalyticsRepository:
        """Create an analytics repository instance"""
        raise NotImplementedError
    
    @abc.abstractmethod
    def create_event_store(self) -> AbstractEventStore:
        """Create an event store instance"""
        raise NotImplementedError