# DENTAL LAB DDD - Domain-Driven Design Implementation Plan

## 🎯 QËLLIMI I PROJEKTIT

Ky projekt është një rishkrim i plotë i sistemit të laboratorit dentar duke ndjekur parimet e **Domain-Driven Design (DDD)** dhe **Clean Architecture** siç pë<PERSON>hen në librin "Architecture Patterns with Python" nga Harry <PERSON>cival dhe Bob <PERSON>.

### Filozo<PERSON> Kryesore
- **Ndarja e logjikës së biznesit** nga detajet teknike (Django, databaza, API)
- **Zemra e aplikacionit (Domain)** duhet të jetë Python i pastër pa lidhje me Django
- **Infrastruktura** (Django, databaza) janë "adapterë" që lidhin zemrën me botën e jashtme

---

## 📁 STRUKTURA E PROJEKTIT

```
DENTAL_LAB_DDD/
├── README.md
├── requirements.txt
├── pyproject.toml
├── Makefile
├── docker-compose.yml
├── Dockerfile
├── pytest.ini
├── mypy.ini
├── .env.example
├── .gitignore
│
├── src/
│   └── dental_lab/
│       ├── __init__.py
│       ├── config.py
│       │
│       ├── domain/                          # ❤️ ZEMRA - Python i pastër
│       │   ├── __init__.py
│       │   ├── model.py                     # Domain entities & value objects
│       │   ├── events.py                    # Domain events
│       │   ├── exceptions.py                # Domain exceptions
│       │   └── commands.py                  # Domain commands
│       │
│       ├── service_layer/                   # 🎭 ORKESTRIMI
│       │   ├── __init__.py
│       │   ├── services.py                  # Use case implementations
│       │   ├── uow.py                       # Unit of Work pattern
│       │   ├── handlers.py                  # Command & event handlers
│       │   └── messagebus.py                # Internal message bus
│       │
│       ├── adapters/                        # 🔌 URAT TEKNIKË
│       │   ├── __init__.py
│       │   ├── repository.py                # Repository implementations
│       │   ├── orm.py                       # Django ORM models (thin)
│       │   ├── email.py                     # Email adapter
│       │   ├── notifications.py             # Notification adapter
│       │   └── external_apis.py             # External API adapters
│       │
│       └── entrypoints/                     # 🚪 PIKAT E HYRJES
│           ├── __init__.py
│           └── django_project/              # Django web application
│               ├── manage.py
│               ├── dental_lab_web/
│               │   ├── __init__.py
│               │   ├── settings.py
│               │   ├── urls.py
│               │   └── wsgi.py
│               ├── web/                     # Thin Django views
│               │   ├── __init__.py
│               │   ├── views.py
│               │   ├── forms.py
│               │   ├── urls.py
│               │   └── serializers.py
│               ├── templates/
│               └── static/
│
├── tests/                                   # 🧪 TESTE TË ORGANIZUARA
│   ├── conftest.py
│   ├── unit/                               # Teste të shpejta për domain
│   │   ├── test_domain_models.py
│   │   ├── test_services.py
│   │   └── test_handlers.py
│   ├── integration/                        # Teste për adapterë
│   │   ├── test_repository.py
│   │   ├── test_uow.py
│   │   └── test_orm.py
│   └── e2e/                               # Teste end-to-end
│       ├── test_api.py
│       └── test_workflows.py
│
├── scripts/                                # 📜 SKRIPTE NDIHMËSE
│   ├── bootstrap.py
│   ├── populate_test_data.py
│   └── migration_from_old_system.py
│
└── docs/                                   # 📚 DOKUMENTACIONI
    ├── architecture.md
    ├── domain_model.md
    ├── api_documentation.md
    └── deployment.md
```

---

## 🏗️ PLAN IMPLEMENTIMI - 5 FAZA

### FAZA 0: Përgatitja dhe Konfigurimi (1-2 orë)
**Qëllimi**: Krijimi i strukturës së projektit dhe mjedisit të zhvillimit

#### Aktivitete:
1. ✅ Krijimi i strukturës së skedarëve
2. ⏳ Konfigurimi i mjedisit virtual
3. ⏳ Instalimi i dependency-eve bazë
4. ⏳ Konfigurimi i pytest, mypy, dhe tooling-ut
5. ⏳ Setup i Docker environment

#### Skedarët për t'u krijuar:
- `requirements.txt`
- `pyproject.toml` 
- `pytest.ini`
- `mypy.ini`
- `Makefile`
- `docker-compose.yml`

---

### FAZA 1: Ndërtimi i Zemrës - Domain Layer (3-5 orë)
**Qëllimi**: Krijimi i logjikës thelbësore të biznesit pa asnjë lidhje me Django

#### 1.1 Domain Entities (src/dental_lab/domain/model.py)

```python
# Domain Entities - Python i pastër
from datetime import datetime, timedelta
from typing import List, Optional, Set
from decimal import Decimal
from enum import Enum

class CaseStatus(Enum):
    PENDING_ACCEPTANCE = "pending_acceptance"
    IN_PROGRESS = "in_progress"
    QUALITY_CHECK = "quality_check"
    READY_TO_SHIP = "ready_to_ship"
    COMPLETED = "completed"
    CANCELLED = "cancelled"

class Case:
    def __init__(
        self, 
        case_number: int,
        dentist_id: int,
        patient_id: Optional[int] = None,
        status: CaseStatus = CaseStatus.PENDING_ACCEPTANCE,
        deadline: Optional[datetime] = None,
        created_at: Optional[datetime] = None
    ):
        self.case_number = case_number
        self.dentist_id = dentist_id
        self.patient_id = patient_id
        self.status = status
        self.deadline = deadline
        self.created_at = created_at or datetime.now()
        self.items: List[CaseItem] = []
        self.tasks: List[Task] = []
        self.events: List = []  # Domain events
        
    def add_item(self, item_id: int, quantity: int, unit_id: int) -> None:
        case_item = CaseItem(
            case_number=self.case_number,
            item_id=item_id,
            quantity=quantity,
            unit_id=unit_id
        )
        self.items.append(case_item)
        # Shto domain event
        self.events.append(
            ItemAddedToCase(
                case_number=self.case_number,
                item_id=item_id,
                quantity=quantity
            )
        )
    
    def is_overdue(self) -> bool:
        if self.deadline and self.status not in [
            CaseStatus.COMPLETED, 
            CaseStatus.CANCELLED
        ]:
            return datetime.now() > self.deadline
        return False
    
    def can_be_shipped(self) -> bool:
        return self.status == CaseStatus.READY_TO_SHIP
    
    def complete_case(self) -> None:
        if self.status == CaseStatus.READY_TO_SHIP:
            self.status = CaseStatus.COMPLETED
            self.events.append(
                CaseCompleted(case_number=self.case_number)
            )
        else:
            raise ValueError("Case must be ready to ship before completion")

class Patient:
    def __init__(
        self,
        patient_id: int,
        first_name: str,
        last_name: str,
        dentist_id: int,
        phone_number: Optional[str] = None,
        email: Optional[str] = None
    ):
        self.patient_id = patient_id
        self.first_name = first_name
        self.last_name = last_name
        self.dentist_id = dentist_id
        self.phone_number = phone_number
        self.email = email
        
    @property
    def full_name(self) -> str:
        return f"{self.first_name} {self.last_name}".strip()

class Dentist:
    def __init__(
        self,
        dentist_id: int,
        first_name: str,
        last_name: str,
        clinic_name: str,
        phone_number: str
    ):
        self.dentist_id = dentist_id
        self.first_name = first_name
        self.last_name = last_name
        self.clinic_name = clinic_name
        self.phone_number = phone_number
        
    @property
    def full_name(self) -> str:
        return f"{self.first_name} {self.last_name}".strip()

# Value Objects
class Money:
    def __init__(self, amount: Decimal, currency: str = "EUR"):
        self.amount = amount
        self.currency = currency
    
    def __eq__(self, other):
        return self.amount == other.amount and self.currency == other.currency
    
    def __add__(self, other):
        if self.currency != other.currency:
            raise ValueError("Cannot add different currencies")
        return Money(self.amount + other.amount, self.currency)

# Domain Events dhe Commands do shkruhen në skedarë të veçantë
```

#### 1.2 Domain Events (src/dental_lab/domain/events.py)

```python
from datetime import datetime
from dataclasses import dataclass

@dataclass
class DomainEvent:
    occurred_at: datetime = None
    
    def __post_init__(self):
        if self.occurred_at is None:
            self.occurred_at = datetime.now()

@dataclass
class CaseCreated(DomainEvent):
    case_number: int
    dentist_id: int
    patient_id: int = None

@dataclass
class ItemAddedToCase(DomainEvent):
    case_number: int
    item_id: int
    quantity: int

@dataclass
class CaseCompleted(DomainEvent):
    case_number: int
    completion_time: datetime = None
```

---

### FAZA 2: Shtresa e Adapterëve - Repository & ORM (2-3 orë)
**Qëllimi**: Krijimi i "urës" midis domain-it dhe infrastrukturës

#### 2.1 Repository Interfaces (src/dental_lab/adapters/repository.py)

```python
import abc
from typing import List, Optional
from dental_lab.domain import model

class AbstractRepository(abc.ABC):
    @abc.abstractmethod
    def add(self, entity):
        raise NotImplementedError
    
    @abc.abstractmethod
    def get(self, id) -> Optional[object]:
        raise NotImplementedError

class AbstractCaseRepository(AbstractRepository):
    @abc.abstractmethod
    def get_by_case_number(self, case_number: int) -> Optional[model.Case]:
        raise NotImplementedError
    
    @abc.abstractmethod
    def list_active_cases(self) -> List[model.Case]:
        raise NotImplementedError
    
    @abc.abstractmethod
    def list_overdue_cases(self) -> List[model.Case]:
        raise NotImplementedError

class AbstractPatientRepository(AbstractRepository):
    @abc.abstractmethod
    def get_by_dentist(self, dentist_id: int) -> List[model.Patient]:
        raise NotImplementedError

class AbstractDentistRepository(AbstractRepository):
    @abc.abstractmethod
    def get_by_user_id(self, user_id: int) -> Optional[model.Dentist]:
        raise NotImplementedError
```

#### 2.2 Django ORM Models - Thin Models (src/dental_lab/adapters/orm.py)

```python
# Këto modele janë VETËM për databazën - pa logjikë biznesi
from django.db import models
from django.utils import timezone

class Case(models.Model):
    case_number = models.AutoField(primary_key=True)
    dentist_id = models.IntegerField()
    patient_id = models.IntegerField(null=True, blank=True)
    status = models.CharField(max_length=30, default='pending_acceptance')
    deadline = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    
    class Meta:
        db_table = 'cases'

class Patient(models.Model):
    patient_id = models.AutoField(primary_key=True)
    first_name = models.CharField(max_length=50)
    last_name = models.CharField(max_length=50)
    dentist_id = models.IntegerField()
    phone_number = models.CharField(max_length=15, null=True, blank=True)
    email = models.EmailField(null=True, blank=True)
    
    class Meta:
        db_table = 'patients'
```

#### 2.3 Repository Implementations

```python
from typing import List, Optional
from dental_lab.domain import model as domain_model
from . import orm as orm_model

class DjangoCaseRepository(AbstractCaseRepository):
    def add(self, case: domain_model.Case):
        orm_case = orm_model.Case(
            case_number=case.case_number,
            dentist_id=case.dentist_id,
            patient_id=case.patient_id,
            status=case.status.value,
            deadline=case.deadline,
            created_at=case.created_at
        )
        orm_case.save()
        return case
    
    def get_by_case_number(self, case_number: int) -> Optional[domain_model.Case]:
        try:
            orm_case = orm_model.Case.objects.get(case_number=case_number)
            return self._to_domain(orm_case)
        except orm_model.Case.DoesNotExist:
            return None
    
    def _to_domain(self, orm_case) -> domain_model.Case:
        return domain_model.Case(
            case_number=orm_case.case_number,
            dentist_id=orm_case.dentist_id,
            patient_id=orm_case.patient_id,
            status=domain_model.CaseStatus(orm_case.status),
            deadline=orm_case.deadline,
            created_at=orm_case.created_at
        )
```

---

### FAZA 3: Service Layer - Use Cases dhe Unit of Work (2-3 orë)
**Qëllimi**: Orkestrimi i operacioneve të biznesit

#### 3.1 Unit of Work Pattern (src/dental_lab/service_layer/uow.py)

```python
import abc
from dental_lab.adapters import repository

class AbstractUnitOfWork(abc.ABC):
    cases: repository.AbstractCaseRepository
    patients: repository.AbstractPatientRepository
    dentists: repository.AbstractDentistRepository

    def __enter__(self):
        return self

    def __exit__(self, *args):
        self.rollback()

    @abc.abstractmethod
    def commit(self):
        raise NotImplementedError

    @abc.abstractmethod
    def rollback(self):
        raise NotImplementedError

class DjangoUnitOfWork(AbstractUnitOfWork):
    def __init__(self):
        self.cases = repository.DjangoCaseRepository()
        self.patients = repository.DjangoPatientRepository()
        self.dentists = repository.DjangoDentistRepository()

    def commit(self):
        # Django auto-commits individual saves
        pass

    def rollback(self):
        # Django handles rollback via transaction middleware
        pass
```

#### 3.2 Service Layer (src/dental_lab/service_layer/services.py)

```python
from datetime import datetime
from dental_lab.domain import model, exceptions
from . import uow

def create_new_case(
    dentist_id: int,
    patient_id: int = None,
    items: List[dict] = None,
    deadline: datetime = None,
    uow: uow.AbstractUnitOfWork = None
) -> int:
    """
    Use case: Krijimi i një rasti të ri
    
    Returns: case_number i rastit të krijuar
    """
    with uow:
        # Verifikojmë që dentisti ekziston
        dentist = uow.dentists.get(dentist_id)
        if not dentist:
            raise exceptions.DentistNotFound(f"Dentist {dentist_id} not found")
        
        # Verifikojmë pacientin nëse është dhënë
        if patient_id:
            patient = uow.patients.get(patient_id)
            if not patient:
                raise exceptions.PatientNotFound(f"Patient {patient_id} not found")
            
            # Verifikojmë që pacienti i përket dentistit
            if patient.dentist_id != dentist_id:
                raise exceptions.PatientDentistMismatch(
                    "Patient does not belong to this dentist"
                )
        
        # Krijojmë numrin e rastit (mund të jetë më kompleks)
        next_case_number = _get_next_case_number(uow)
        
        # Krijojmë rastin
        new_case = model.Case(
            case_number=next_case_number,
            dentist_id=dentist_id,
            patient_id=patient_id,
            deadline=deadline
        )
        
        # Shtojmë artikujt nëse ka
        if items:
            for item_data in items:
                new_case.add_item(
                    item_id=item_data['item_id'],
                    quantity=item_data['quantity'],
                    unit_id=item_data['unit_id']
                )
        
        # Ruajmë rastin
        uow.cases.add(new_case)
        uow.commit()
        
        return new_case.case_number

def get_case_details(case_number: int, uow: uow.AbstractUnitOfWork) -> model.Case:
    """
    Use case: Marrja e detajeve të rastit
    """
    with uow:
        case = uow.cases.get_by_case_number(case_number)
        if not case:
            raise exceptions.CaseNotFound(f"Case {case_number} not found")
        return case

def list_overdue_cases(uow: uow.AbstractUnitOfWork) -> List[model.Case]:
    """
    Use case: Listimi i rasteve të vonuara
    """
    with uow:
        return uow.cases.list_overdue_cases()

def _get_next_case_number(uow: uow.AbstractUnitOfWork) -> int:
    """Logjikë për gjenerimin e numrit të rastit"""
    # Mund të jetë më kompleks - p.sh. bazuar në vitin, departamentin, etj.
    return 1000  # Simplified for now
```

---

### FAZA 4: Django Entrypoints - Thin Views (1-2 orë)
**Qëllimi**: Krijimi i view-eve sa më të holla që të jetë e mundur

#### 4.1 Django Views (src/dental_lab/entrypoints/django_project/web/views.py)

```python
from django.shortcuts import render, redirect
from django.contrib import messages
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
import json

from dental_lab.service_layer import services, uow
from dental_lab.domain import exceptions

def create_case_view(request):
    """
    Thin view - vetëm për koordinimin e input/output
    """
    if request.method == 'POST':
        try:
            # Marrim të dhënat nga request
            dentist_id = int(request.POST.get('dentist_id'))
            patient_id = request.POST.get('patient_id')
            patient_id = int(patient_id) if patient_id else None
            
            # Thirrja e service-it
            case_number = services.create_new_case(
                dentist_id=dentist_id,
                patient_id=patient_id,
                uow=uow.DjangoUnitOfWork()
            )
            
            messages.success(request, f"Rasti #{case_number} u krijua me sukses!")
            return redirect('case_detail', case_number=case_number)
            
        except exceptions.DentistNotFound as e:
            messages.error(request, "Dentisti nuk u gjet")
        except exceptions.PatientNotFound as e:
            messages.error(request, "Pacienti nuk u gjet")
        except exceptions.PatientDentistMismatch as e:
            messages.error(request, "Pacienti nuk i përket këtij dentisti")
        except ValueError as e:
            messages.error(request, f"Gabim në të dhënat: {str(e)}")
    
    # GET request - shfaq formën
    context = {
        'dentists': _get_dentists(),  # Helper function
        'patients': _get_patients()   # Helper function
    }
    return render(request, 'cases/create_case.html', context)

@require_http_methods(["GET"])
def case_detail_view(request, case_number):
    """
    View për shfaqjen e detajeve të rastit
    """
    try:
        case = services.get_case_details(
            case_number=case_number,
            uow=uow.DjangoUnitOfWork()
        )
        context = {'case': case}
        return render(request, 'cases/case_detail.html', context)
        
    except exceptions.CaseNotFound:
        messages.error(request, f"Rasti #{case_number} nuk u gjet")
        return redirect('case_list')

@require_http_methods(["GET"])
def overdue_cases_api(request):
    """
    API endpoint për rastet e vonuara
    """
    try:
        overdue_cases = services.list_overdue_cases(
            uow=uow.DjangoUnitOfWork()
        )
        
        # Konvertojmë në dictionary për JSON
        cases_data = [
            {
                'case_number': case.case_number,
                'dentist_id': case.dentist_id,
                'status': case.status.value,
                'deadline': case.deadline.isoformat() if case.deadline else None,
                'is_overdue': case.is_overdue()
            }
            for case in overdue_cases
        ]
        
        return JsonResponse({'cases': cases_data})
        
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)
```

---

### FAZA 5: Testing dhe CI/CD Setup (2-3 orë)
**Qëllimi**: Krijimi i një sistemi testimi të plotë dhe deployment

#### 5.1 Unit Tests (tests/unit/test_domain_models.py)

```python
import pytest
from datetime import datetime, timedelta
from dental_lab.domain import model, exceptions

class TestCase:
    def test_case_creation(self):
        case = model.Case(
            case_number=1001,
            dentist_id=1,
            patient_id=1,
            deadline=datetime.now() + timedelta(days=7)
        )
        
        assert case.case_number == 1001
        assert case.dentist_id == 1
        assert case.status == model.CaseStatus.PENDING_ACCEPTANCE
        assert not case.is_overdue()
    
    def test_overdue_case(self):
        past_deadline = datetime.now() - timedelta(days=1)
        case = model.Case(
            case_number=1002,
            dentist_id=1,
            deadline=past_deadline
        )
        
        assert case.is_overdue()
    
    def test_case_completion(self):
        case = model.Case(case_number=1003, dentist_id=1)
        case.status = model.CaseStatus.READY_TO_SHIP
        
        case.complete_case()
        
        assert case.status == model.CaseStatus.COMPLETED
        assert len(case.events) == 1
        assert isinstance(case.events[0], model.CaseCompleted)
    
    def test_cannot_complete_case_not_ready(self):
        case = model.Case(case_number=1004, dentist_id=1)
        # Status është PENDING_ACCEPTANCE
        
        with pytest.raises(ValueError, match="must be ready to ship"):
            case.complete_case()
```

#### 5.2 Integration Tests (tests/integration/test_repository.py)

```python
import pytest
from dental_lab.domain import model
from dental_lab.adapters import repository

@pytest.mark.django_db
class TestCaseRepository:
    def test_can_save_a_case(self):
        case = model.Case(
            case_number=1001,
            dentist_id=1,
            patient_id=1
        )
        
        repo = repository.DjangoCaseRepository()
        repo.add(case)
        
        # Verifikojmë që është ruajtur në databazë
        retrieved_case = repo.get_by_case_number(1001)
        
        assert retrieved_case.case_number == case.case_number
        assert retrieved_case.dentist_id == case.dentist_id
        assert retrieved_case.status == case.status
```

#### 5.3 Service Tests (tests/unit/test_services.py)

```python
import pytest
from unittest.mock import Mock
from dental_lab.service_layer import services
from dental_lab.domain import model, exceptions

class FakeCaseRepository:
    def __init__(self):
        self._cases = {}
        self._next_id = 1000
    
    def add(self, case):
        self._cases[case.case_number] = case
        return case
    
    def get_by_case_number(self, case_number):
        return self._cases.get(case_number)

class FakeUnitOfWork:
    def __init__(self):
        self.cases = FakeCaseRepository()
        self.patients = Mock()
        self.dentists = Mock()
        self.committed = False
    
    def __enter__(self):
        return self
    
    def __exit__(self, *args):
        pass
    
    def commit(self):
        self.committed = True
    
    def rollback(self):
        pass

class TestCaseServices:
    def test_create_case_success(self):
        uow = FakeUnitOfWork()
        # Setup mock returns
        uow.dentists.get.return_value = model.Dentist(1, "John", "Doe", "Clinic", "123")
        uow.patients.get.return_value = model.Patient(1, "Jane", "Smith", 1)
        
        case_number = services.create_new_case(
            dentist_id=1,
            patient_id=1,
            uow=uow
        )
        
        assert case_number == 1000
        assert uow.committed
        assert len(uow.cases._cases) == 1
    
    def test_create_case_dentist_not_found(self):
        uow = FakeUnitOfWork()
        uow.dentists.get.return_value = None  # Dentist not found
        
        with pytest.raises(exceptions.DentistNotFound):
            services.create_new_case(
                dentist_id=999,
                patient_id=1,
                uow=uow
            )
```

---

## 🚀 STRATEGJIA E IMPLEMENTIMIT

### Prioriteti i Moduleve
1. **Case Management** (Core business)
2. **Patient & Dentist Management** 
3. **Item & Inventory Management**
4. **Billing & Finance**
5. **Scheduling & Workflow**
6. **Notifications & Reporting**

### Principet e Zhvillimit
- **Test-Driven Development (TDD)**: Shkruaj testet para kodit
- **Domain-First**: Fillo me domain-in, pastaj adapters
- **Iterative**: Ndërtoje modul pas moduli
- **Clean Interfaces**: Dependency inversion në çdo shtresë

### Tools dhe Libraries
- **Domain Layer**: Python i pastër (no dependencies)
- **Testing**: pytest, pytest-django
- **ORM**: Django ORM (vetëm për persistence)
- **API**: Django REST Framework (nëse nevojitet)
- **Message Bus**: Redis (për event-driven architecture)
- **Containerization**: Docker & Docker Compose

---

## 📋 TASK LIST PER IMPLEMENTIM

### ✅ E KRYER
- [x] Analiza e projektit ekzistues
- [x] Krijimi i strukturës së projektit
- [x] Dokumentimi i planit të implementimit

### ⏳ NË PRITJE
- [ ] Faza 0: Setup i mjedisit
- [ ] Faza 1: Domain Layer
- [ ] Faza 2: Repository & ORM
- [ ] Faza 3: Service Layer
- [ ] Faza 4: Django Views
- [ ] Faza 5: Testing & CI/CD

### 🎯 NEXT STEPS
1. **Setup environment** (requirements.txt, Docker, etc.)
2. **Implement core domain entities** (Case, Patient, Dentist)
3. **Create repository interfaces**
4. **Build first use case** (Create Case)
5. **Add comprehensive tests**

---

## 💡 BENEFITS TË ARKITEKTURËS SË RE

### ✅ Avantazhet
- **Testability**: Unit tests të shpejta pa databazë
- **Maintainability**: Separation of concerns i qartë
- **Flexibility**: E lehtë për të ndryshuar teknologjitë
- **Scalability**: Domain model i pavarur nga infrastruktura
- **Team Collaboration**: Shtresa të qarta lejojnë punë paralele

### 🔄 Migration Strategy
- **Gradual Migration**: Moduli i ri pas modulit të vjetër
- **Database Compatibility**: Përdor të njëjtat tabela fillimisht
- **Feature Toggle**: Mund të kalosh ndërmjet implementimeve
- **Data Migration**: Scripts për transferimin e të dhënave

---

*Ky dokument do të përditësohet gjatë procesit të implementimit për të pasqyruar përparimin dhe ndryshimet.*