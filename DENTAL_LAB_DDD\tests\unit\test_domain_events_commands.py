# DENTAL LAB DDD - Unit Tests for Domain Events and Commands
# Tests for domain events and command validation

import pytest
from datetime import datetime, timedelta
from decimal import Decimal

from dental_lab.domain import events, commands


class TestDomainEvents:
    """Test domain events"""
    
    def test_domain_event_base_class(self):
        """Test base domain event functionality"""
        now = datetime.now()
        event = events.DomainEvent(occurred_at=now)
        
        assert event.occurred_at == now
    
    def test_domain_event_auto_timestamp(self):
        """Test that events get automatic timestamp if not provided"""
        event = events.DomainEvent(occurred_at=None)
        
        assert event.occurred_at is not None
        assert isinstance(event.occurred_at, datetime)
        # Should be very recent
        assert (datetime.now() - event.occurred_at).total_seconds() < 1
    
    def test_case_created_event(self):
        """Test CaseCreated event"""
        event = events.CaseCreated(
            case_number=1001,
            dentist_id=100,
            patient_id=1,
            created_by=500,
            occurred_at=datetime.now()
        )
        
        assert event.case_number == 1001
        assert event.dentist_id == 100
        assert event.patient_id == 1
        assert event.created_by == 500
        assert isinstance(event.occurred_at, datetime)
    
    def test_case_completed_event(self):
        """Test CaseCompleted event"""
        completion_time = datetime.now()
        event = events.CaseCompleted(
            case_number=1001,
            completed_at=completion_time,
            total_duration="PT48H",  # ISO 8601 duration: 48 hours
            completed_by=500,
            occurred_at=completion_time
        )
        
        assert event.case_number == 1001
        assert event.completed_at == completion_time
        assert event.total_duration == "PT48H"
        assert event.completed_by == 500
    
    def test_item_added_to_case_event(self):
        """Test ItemAddedToCase event"""
        event = events.ItemAddedToCase(
            case_number=1001,
            item_id=50,
            quantity=2,
            unit_id=1,
            estimated_time="PT3H",  # 3 hours
            added_by=500,
            occurred_at=datetime.now()
        )
        
        assert event.case_number == 1001
        assert event.item_id == 50
        assert event.quantity == 2
        assert event.unit_id == 1
        assert event.estimated_time == "PT3H"
    
    def test_task_started_event(self):
        """Test TaskStarted event"""
        start_time = datetime.now()
        event = events.TaskStarted(
            task_id=1,
            case_number=1001,
            started_at=start_time,
            started_by=500,
            occurred_at=start_time
        )
        
        assert event.task_id == 1
        assert event.case_number == 1001
        assert event.started_at == start_time
        assert event.started_by == 500
    
    def test_quality_check_failed_event(self):
        """Test QualityCheckFailed event"""
        failed_items = {
            "crown_fit": "Poor marginal adaptation",
            "color_match": "Color too dark"
        }
        required_actions = {
            "crown_fit": "Remake crown",
            "color_match": "Adjust shade"
        }
        
        event = events.QualityCheckFailed(
            case_number=1001,
            quality_checker=300,
            failed_items=failed_items,
            required_actions=required_actions,
            occurred_at=datetime.now()
        )
        
        assert event.case_number == 1001
        assert event.quality_checker == 300
        assert event.failed_items == failed_items
        assert event.required_actions == required_actions
    
    def test_deadline_approaching_event(self):
        """Test DeadlineApproaching event"""
        deadline = datetime.now() + timedelta(days=2)
        
        event = events.DeadlineApproaching(
            case_number=1001,
            deadline=deadline,
            days_remaining=2,
            dentist_id=100,
            assigned_technicians=[500, 501],
            occurred_at=datetime.now()
        )
        
        assert event.case_number == 1001
        assert event.deadline == deadline
        assert event.days_remaining == 2
        assert event.dentist_id == 100
        assert event.assigned_technicians == [500, 501]


class TestDomainCommands:
    """Test domain commands"""
    
    def test_command_base_class(self):
        """Test base command functionality"""
        now = datetime.now()
        command = commands.Command(
            command_id="cmd-123",
            issued_by=500,
            issued_at=now
        )
        
        assert command.command_id == "cmd-123"
        assert command.issued_by == 500
        assert command.issued_at == now
    
    def test_command_auto_timestamp(self):
        """Test that commands get automatic timestamp if not provided"""
        command = commands.Command(issued_at=None)
        
        assert command.issued_at is not None
        assert isinstance(command.issued_at, datetime)
        # Should be very recent
        assert (datetime.now() - command.issued_at).total_seconds() < 1
    
    def test_create_case_command(self):
        """Test CreateCase command"""
        deadline = datetime.now() + timedelta(days=7)
        
        command = commands.CreateCase(
            dentist_id=100,
            patient_id=1,
            deadline=deadline,
            priority=3,  # High priority
            notes="Urgent case",
            special_requirements="Rush delivery",
            workflow_template_id=5,
            issued_by=500
        )
        
        assert command.dentist_id == 100
        assert command.patient_id == 1
        assert command.deadline == deadline
        assert command.priority == 3
        assert command.notes == "Urgent case"
        assert command.special_requirements == "Rush delivery"
        assert command.workflow_template_id == 5
        assert command.issued_by == 500
    
    def test_add_item_to_case_command(self):
        """Test AddItemToCase command"""
        estimated_time = timedelta(hours=3)
        
        command = commands.AddItemToCase(
            case_number=1001,
            item_id=50,
            quantity=2,
            unit_id=1,
            estimated_time=estimated_time,
            special_instructions="Extra polish required",
            issued_by=500
        )
        
        assert command.case_number == 1001
        assert command.item_id == 50
        assert command.quantity == 2
        assert command.unit_id == 1
        assert command.estimated_time == estimated_time
        assert command.special_instructions == "Extra polish required"
    
    def test_start_case_command(self):
        """Test StartCase command"""
        received_time = datetime.now()
        estimated_completion = received_time + timedelta(days=5)
        
        command = commands.StartCase(
            case_number=1001,
            received_date_time=received_time,
            estimated_completion=estimated_completion,
            issued_by=500
        )
        
        assert command.case_number == 1001
        assert command.received_date_time == received_time
        assert command.estimated_completion == estimated_completion
    
    def test_complete_case_command(self):
        """Test CompleteCase command"""
        command = commands.CompleteCase(
            case_number=1001,
            completion_notes="All items completed successfully",
            quality_approved_by=300,
            issued_by=500
        )
        
        assert command.case_number == 1001
        assert command.completion_notes == "All items completed successfully"
        assert command.quality_approved_by == 300
    
    def test_cancel_case_command(self):
        """Test CancelCase command"""
        command = commands.CancelCase(
            case_number=1001,
            reason="Patient cancelled order",
            refund_amount=Decimal('150.00'),
            notify_dentist=True,
            issued_by=500
        )
        
        assert command.case_number == 1001
        assert command.reason == "Patient cancelled order"
        assert command.refund_amount == Decimal('150.00')
        assert command.notify_dentist == True
    
    def test_assign_task_command(self):
        """Test AssignTask command"""
        deadline = datetime.now() + timedelta(days=2)
        
        command = commands.AssignTask(
            task_id=1,
            assigned_to=500,
            assignment_notes="High priority task",
            deadline=deadline,
            issued_by=400
        )
        
        assert command.task_id == 1
        assert command.assigned_to == 500
        assert command.assignment_notes == "High priority task"
        assert command.deadline == deadline
        assert command.issued_by == 400
    
    def test_create_task_command(self):
        """Test CreateTask command"""
        estimated_duration = timedelta(hours=2, minutes=30)
        dependencies = [2, 3]  # Task IDs
        
        command = commands.CreateTask(
            case_number=1001,
            title="Polish final restoration",
            description="Final polish and quality check",
            estimated_duration=estimated_duration,
            priority=3,  # High priority
            workflow_stage_id=5,
            case_item_id=10,
            dependencies=dependencies,
            issued_by=500
        )
        
        assert command.case_number == 1001
        assert command.title == "Polish final restoration"
        assert command.description == "Final polish and quality check"
        assert command.estimated_duration == estimated_duration
        assert command.priority == 3
        assert command.workflow_stage_id == 5
        assert command.case_item_id == 10
        assert command.dependencies == dependencies
    
    def test_register_patient_command(self):
        """Test RegisterPatient command"""
        birth_date = datetime(1985, 6, 15)
        
        command = commands.RegisterPatient(
            dentist_id=100,
            first_name="Alice",
            last_name="Johnson",
            phone_number="555-0123",
            email="<EMAIL>",
            address="456 Oak St, City, State",
            date_of_birth=birth_date,
            medical_notes="No known allergies",
            issued_by=500
        )
        
        assert command.dentist_id == 100
        assert command.first_name == "Alice"
        assert command.last_name == "Johnson"
        assert command.phone_number == "555-0123"
        assert command.email == "<EMAIL>"
        assert command.address == "456 Oak St, City, State"
        assert command.date_of_birth == birth_date
        assert command.medical_notes == "No known allergies"
    
    def test_start_quality_check_command(self):
        """Test StartQualityCheck command"""
        checklist = {
            "marginal_fit": True,
            "color_match": True,
            "surface_finish": False,
            "occlusion": True
        }
        
        command = commands.StartQualityCheck(
            case_number=1001,
            quality_checker=300,
            checklist_items=checklist,
            notes="Standard quality check procedure",
            issued_by=400
        )
        
        assert command.case_number == 1001
        assert command.quality_checker == 300
        assert command.checklist_items == checklist
        assert command.notes == "Standard quality check procedure"
    
    def test_fail_quality_check_command(self):
        """Test FailQualityCheck command"""
        failed_items = {
            "marginal_fit": "Gap detected at gingival margin",
            "color_match": "Shade too light"
        }
        required_corrections = [
            "Adjust margins",
            "Re-stain to match A3 shade"
        ]
        
        command = commands.FailQualityCheck(
            case_number=1001,
            quality_checker=300,
            failed_items=failed_items,
            required_corrections=required_corrections,
            severity="major",
            issued_by=400
        )
        
        assert command.case_number == 1001
        assert command.quality_checker == 300
        assert command.failed_items == failed_items
        assert command.required_corrections == required_corrections
        assert command.severity == "major"
    
    def test_send_deadline_reminder_command(self):
        """Test SendDeadlineReminder command"""
        technicians = [500, 501, 502]
        
        command = commands.SendDeadlineReminder(
            case_number=1001,
            days_until_deadline=2,
            recipient_dentist_id=100,
            recipient_technicians=technicians,
            issued_by=400
        )
        
        assert command.case_number == 1001
        assert command.days_until_deadline == 2
        assert command.recipient_dentist_id == 100
        assert command.recipient_technicians == technicians
    
    def test_batch_update_cases_command(self):
        """Test BatchUpdateCases command"""
        case_numbers = [1001, 1002, 1003]
        update_data = {
            "priority": 3,
            "responsible_department_id": 5
        }
        
        command = commands.BatchUpdateCases(
            case_numbers=case_numbers,
            update_data=update_data,
            reason="Emergency priority increase",
            issued_by=400
        )
        
        assert command.case_numbers == case_numbers
        assert command.update_data == update_data
        assert command.reason == "Emergency priority increase"
    
    def test_generate_case_report_command(self):
        """Test GenerateCaseReport command"""
        command = commands.GenerateCaseReport(
            case_number=1001,
            report_type="detailed",
            include_attachments=True,
            recipient_email="<EMAIL>",
            issued_by=500
        )
        
        assert command.case_number == 1001
        assert command.report_type == "detailed"
        assert command.include_attachments == True
        assert command.recipient_email == "<EMAIL>"
    
    def test_select_teeth_for_case_command(self):
        """Test SelectTeethForCase command"""
        tooth_numbers = [1, 2, 3, 16, 17, 32]
        
        command = commands.SelectTeethForCase(
            case_number=1001,
            tooth_numbers=tooth_numbers,
            teeth_color="A2",
            special_notes="Patient prefers lighter shade",
            issued_by=500
        )
        
        assert command.case_number == 1001
        assert command.tooth_numbers == tooth_numbers
        assert command.teeth_color == "A2"
        assert command.special_notes == "Patient prefers lighter shade"


class TestEventCommandIntegration:
    """Test integration between events and commands"""
    
    def test_command_generates_event_data(self):
        """Test that command data can be used to create events"""
        # Create a command
        create_command = commands.CreateCase(
            dentist_id=100,
            patient_id=1,
            deadline=datetime.now() + timedelta(days=7),
            notes="Test case creation",
            issued_by=500
        )
        
        # Use command data to create corresponding event
        created_event = events.CaseCreated(
            case_number=1001,  # This would be generated by the system
            dentist_id=create_command.dentist_id,
            patient_id=create_command.patient_id,
            created_by=create_command.issued_by,
            occurred_at=datetime.now()
        )
        
        assert created_event.dentist_id == create_command.dentist_id
        assert created_event.patient_id == create_command.patient_id
        assert created_event.created_by == create_command.issued_by
    
    def test_task_lifecycle_commands_and_events(self):
        """Test task lifecycle through commands and events"""
        # Create task command
        create_cmd = commands.CreateTask(
            case_number=1001,
            title="Test task",
            description="Test description",
            estimated_duration=timedelta(hours=2),
            issued_by=500
        )
        
        # Start task command
        start_cmd = commands.StartTask(
            task_id=1,  # Generated by system
            start_notes="Beginning work",
            issued_by=500
        )
        
        # Complete task command
        complete_cmd = commands.CompleteTask(
            task_id=1,
            completion_notes="Task finished successfully",
            actual_duration=timedelta(hours=1, minutes=45),
            quality_check_passed=True,
            issued_by=500
        )
        
        # Corresponding events
        created_event = events.TaskCreated(
            task_id=1,
            case_number=create_cmd.case_number,
            title=create_cmd.title,
            estimated_duration="PT2H",  # ISO duration
            created_by=create_cmd.issued_by,
            occurred_at=datetime.now()
        )
        
        started_event = events.TaskStarted(
            task_id=start_cmd.task_id,
            case_number=1001,
            started_at=datetime.now(),
            started_by=start_cmd.issued_by,
            occurred_at=datetime.now()
        )
        
        completed_event = events.TaskCompleted(
            task_id=complete_cmd.task_id,
            case_number=1001,
            completed_at=datetime.now(),
            actual_duration="PT1H45M",  # 1 hour 45 minutes
            quality_check_passed=complete_cmd.quality_check_passed,
            completed_by=complete_cmd.issued_by,
            occurred_at=datetime.now()
        )
        
        # Verify the command-event flow makes sense
        assert created_event.case_number == create_cmd.case_number
        assert started_event.task_id == start_cmd.task_id
        assert completed_event.quality_check_passed == complete_cmd.quality_check_passed