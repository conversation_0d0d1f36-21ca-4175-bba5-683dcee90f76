# DENTAL LAB DDD - Domain-Driven Design Implementation

🦷 **Sistema e Laboratorit Dentar e Ndërtuar me DDD dhe Clean Architecture**

## 🎯 Qëllimi

Ky projekt është një rishkrim i plotë i sistemit të laboratorit dentar duke ndjekur parimet e **Domain-Driven Design (DDD)** dhe **Clean Architecture** siç pë<PERSON>kruhen në librin "Architecture Patterns with Python".

## 🏗️ Arkitektura

```
Domain Layer (Python i pastër) 
    ↑
Service Layer (Use Cases)
    ↑
Adapters Layer (Repository, ORM)
    ↑
Entrypoints (Django Views, API)
```

## 📁 Struktura e Projektit

```
DENTAL_LAB_DDD/
├── src/dental_lab/           # Source code
│   ├── domain/              # ❤️ Business logic (Python i pastër)
│   ├── service_layer/       # 🎭 Use cases dhe orchestration
│   ├── adapters/           # 🔌 Repository, ORM, External APIs
│   └── entrypoints/        # 🚪 Django views, API endpoints
├── tests/                  # 🧪 Tests të organizuara
│   ├── unit/              # Domain tests (të shpejta)
│   ├── integration/       # Repository tests
│   └── e2e/              # End-to-end tests
├── scripts/               # 📜 Utility scripts
└── docs/                 # 📚 Documentation
```

## 🚀 Quick Start

```bash
# Clone the project
git clone <repository-url>
cd DENTAL_LAB_DDD

# Setup virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Run tests
pytest

# Start development server
cd src/dental_lab/entrypoints/django_project
python manage.py runserver
```

## 📋 Development Status

### ✅ E kryer
- [x] Project structure setup
- [x] Implementation plan documentation

### ⏳ Në zhvillim
- [ ] Domain layer implementation
- [ ] Repository patterns
- [ ] Service layer
- [ ] Django integration
- [ ] Comprehensive testing

## 🔄 Migration nga Sistemi i Vjetër

Ky projekt është krijuar për të zëvendësuar sistemin ekzistues të laboratorit dentar. Strategjia e migrimit:

1. **Gradual Migration**: Module pas moduli
2. **Database Compatibility**: Përdor të njëjtat tabela fillimisht
3. **Feature Flags**: Lejonte kalimin ndërmjet implementimeve
4. **Data Migration**: Scripts për transferimin e të dhënave

## 📚 Dokumentacioni

- [Implementation Plan](PROJECT_STRUCTURE_AND_IMPLEMENTATION_PLAN.md)
- [Architecture Details](docs/architecture.md) *(coming soon)*
- [Domain Model](docs/domain_model.md) *(coming soon)*
- [API Documentation](docs/api_documentation.md) *(coming soon)*

## 🛠️ Tech Stack

- **Domain Layer**: Python i pastër (pa dependencies)
- **Service Layer**: Python, dependency injection
- **Adapters**: Django ORM, Redis, External APIs
- **Entrypoints**: Django, Django REST Framework
- **Testing**: pytest, pytest-django
- **Deployment**: Docker, Docker Compose

## 📖 Inspired by

- "Architecture Patterns with Python" nga Harry Percival dhe Bob Gregory
- Domain-Driven Design principles
- Clean Architecture concepts

---

*Projekti është në zhvillim aktiv. Për më shumë detaje, shiko [Implementation Plan](PROJECT_STRUCTURE_AND_IMPLEMENTATION_PLAN.md).*