# DENTAL LAB DDD - Domain Exceptions
# Business-specific exceptions that express domain concepts

from typing import Optional, List, Dict, Any


class DomainException(Exception):
    """Base exception for all domain-related errors"""
    
    def __init__(self, message: str, error_code: Optional[str] = None, details: Optional[Dict[str, Any]] = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details or {}


# Case-related exceptions
class CaseException(DomainException):
    """Base exception for case-related errors"""
    pass


class CaseNotFound(CaseException):
    """Raised when a case cannot be found"""
    
    def __init__(self, case_number: int):
        super().__init__(
            f"Case #{case_number} not found",
            error_code="CASE_NOT_FOUND",
            details={"case_number": case_number}
        )
        self.case_number = case_number


class InvalidCaseStatus(CaseException):
    """Raised when trying to perform an operation with invalid case status"""
    
    def __init__(self, case_number: int, current_status: str, required_status: str, operation: str):
        super().__init__(
            f"Cannot {operation} case #{case_number}. Current status: {current_status}, required: {required_status}",
            error_code="INVALID_CASE_STATUS",
            details={
                "case_number": case_number,
                "current_status": current_status,
                "required_status": required_status,
                "operation": operation
            }
        )
        self.case_number = case_number
        self.current_status = current_status
        self.required_status = required_status


class CaseAlreadyCompleted(CaseException):
    """Raised when trying to modify a completed case"""
    
    def __init__(self, case_number: int, operation: str):
        super().__init__(
            f"Cannot {operation} case #{case_number}: case is already completed",
            error_code="CASE_ALREADY_COMPLETED",
            details={"case_number": case_number, "operation": operation}
        )


class CaseHasNoItems(CaseException):
    """Raised when trying to start a case without items"""
    
    def __init__(self, case_number: int):
        super().__init__(
            f"Cannot start case #{case_number}: case has no items",
            error_code="CASE_HAS_NO_ITEMS",
            details={"case_number": case_number}
        )


class CaseItemsNotCompleted(CaseException):
    """Raised when trying to complete a case with incomplete items"""
    
    def __init__(self, case_number: int, incomplete_items: List[int]):
        super().__init__(
            f"Cannot complete case #{case_number}: items {incomplete_items} are not completed",
            error_code="CASE_ITEMS_NOT_COMPLETED",
            details={"case_number": case_number, "incomplete_items": incomplete_items}
        )


class InvalidDeadline(CaseException):
    """Raised when setting an invalid deadline"""
    
    def __init__(self, case_number: int, reason: str):
        super().__init__(
            f"Invalid deadline for case #{case_number}: {reason}",
            error_code="INVALID_DEADLINE",
            details={"case_number": case_number, "reason": reason}
        )


# Patient-related exceptions
class PatientException(DomainException):
    """Base exception for patient-related errors"""
    pass


class PatientNotFound(PatientException):
    """Raised when a patient cannot be found"""
    
    def __init__(self, patient_id: int):
        super().__init__(
            f"Patient #{patient_id} not found",
            error_code="PATIENT_NOT_FOUND",
            details={"patient_id": patient_id}
        )
        self.patient_id = patient_id


class PatientDentistMismatch(PatientException):
    """Raised when patient doesn't belong to the specified dentist"""
    
    def __init__(self, patient_id: int, dentist_id: int, actual_dentist_id: int):
        super().__init__(
            f"Patient #{patient_id} belongs to dentist #{actual_dentist_id}, not #{dentist_id}",
            error_code="PATIENT_DENTIST_MISMATCH",
            details={
                "patient_id": patient_id,
                "expected_dentist_id": dentist_id,
                "actual_dentist_id": actual_dentist_id
            }
        )


class InvalidPatientData(PatientException):
    """Raised when patient data is invalid"""
    
    def __init__(self, field: str, value: str, reason: str):
        super().__init__(
            f"Invalid {field} '{value}': {reason}",
            error_code="INVALID_PATIENT_DATA",
            details={"field": field, "value": value, "reason": reason}
        )


# Dentist-related exceptions
class DentistException(DomainException):
    """Base exception for dentist-related errors"""
    pass


class DentistNotFound(DentistException):
    """Raised when a dentist cannot be found"""
    
    def __init__(self, dentist_id: int):
        super().__init__(
            f"Dentist #{dentist_id} not found",
            error_code="DENTIST_NOT_FOUND",
            details={"dentist_id": dentist_id}
        )
        self.dentist_id = dentist_id


class InvalidDentistData(DentistException):
    """Raised when dentist data is invalid"""
    
    def __init__(self, field: str, value: str, reason: str):
        super().__init__(
            f"Invalid {field} '{value}': {reason}",
            error_code="INVALID_DENTIST_DATA",
            details={"field": field, "value": value, "reason": reason}
        )


# Task-related exceptions
class TaskException(DomainException):
    """Base exception for task-related errors"""
    pass


class TaskNotFound(TaskException):
    """Raised when a task cannot be found"""
    
    def __init__(self, task_id: int):
        super().__init__(
            f"Task #{task_id} not found",
            error_code="TASK_NOT_FOUND",
            details={"task_id": task_id}
        )


class InvalidTaskStatus(TaskException):
    """Raised when trying to perform an operation with invalid task status"""
    
    def __init__(self, task_id: int, current_status: str, required_status: str, operation: str):
        super().__init__(
            f"Cannot {operation} task #{task_id}. Current status: {current_status}, required: {required_status}",
            error_code="INVALID_TASK_STATUS",
            details={
                "task_id": task_id,
                "current_status": current_status,
                "required_status": required_status,
                "operation": operation
            }
        )


class TaskDependenciesNotMet(TaskException):
    """Raised when task dependencies are not satisfied"""
    
    def __init__(self, task_id: int, pending_dependencies: List[int]):
        super().__init__(
            f"Cannot start task #{task_id}: dependencies {pending_dependencies} not completed",
            error_code="TASK_DEPENDENCIES_NOT_MET",
            details={"task_id": task_id, "pending_dependencies": pending_dependencies}
        )


class TaskBlocked(TaskException):
    """Raised when task is blocked"""
    
    def __init__(self, task_id: int, blocking_reason: str):
        super().__init__(
            f"Task #{task_id} is blocked: {blocking_reason}",
            error_code="TASK_BLOCKED",
            details={"task_id": task_id, "blocking_reason": blocking_reason}
        )


# Item-related exceptions
class ItemException(DomainException):
    """Base exception for item-related errors"""
    pass


class ItemNotFound(ItemException):
    """Raised when an item cannot be found"""
    
    def __init__(self, item_id: int):
        super().__init__(
            f"Item #{item_id} not found",
            error_code="ITEM_NOT_FOUND",
            details={"item_id": item_id}
        )


class InvalidQuantity(ItemException):
    """Raised when quantity is invalid"""
    
    def __init__(self, quantity: int, reason: str):
        super().__init__(
            f"Invalid quantity {quantity}: {reason}",
            error_code="INVALID_QUANTITY",
            details={"quantity": quantity, "reason": reason}
        )


class ItemAlreadyExists(ItemException):
    """Raised when trying to add an item that already exists in the case"""
    
    def __init__(self, case_number: int, item_id: int):
        super().__init__(
            f"Item #{item_id} already exists in case #{case_number}",
            error_code="ITEM_ALREADY_EXISTS",
            details={"case_number": case_number, "item_id": item_id}
        )


# Workflow-related exceptions
class WorkflowException(DomainException):
    """Base exception for workflow-related errors"""
    pass


class InvalidWorkflowTransition(WorkflowException):
    """Raised when an invalid workflow transition is attempted"""
    
    def __init__(self, case_number: int, from_stage: str, to_stage: str):
        super().__init__(
            f"Invalid workflow transition for case #{case_number}: from {from_stage} to {to_stage}",
            error_code="INVALID_WORKFLOW_TRANSITION",
            details={"case_number": case_number, "from_stage": from_stage, "to_stage": to_stage}
        )


class WorkflowStageNotFound(WorkflowException):
    """Raised when a workflow stage cannot be found"""
    
    def __init__(self, stage_id: int):
        super().__init__(
            f"Workflow stage #{stage_id} not found",
            error_code="WORKFLOW_STAGE_NOT_FOUND",
            details={"stage_id": stage_id}
        )


# Quality-related exceptions
class QualityException(DomainException):
    """Base exception for quality-related errors"""
    pass


class QualityCheckFailed(QualityException):
    """Raised when quality check fails"""
    
    def __init__(self, case_number: int, failed_items: Dict[str, str]):
        super().__init__(
            f"Quality check failed for case #{case_number}",
            error_code="QUALITY_CHECK_FAILED",
            details={"case_number": case_number, "failed_items": failed_items}
        )


class QualityCheckNotCompleted(QualityException):
    """Raised when trying to proceed without completed quality check"""
    
    def __init__(self, case_number: int):
        super().__init__(
            f"Quality check not completed for case #{case_number}",
            error_code="QUALITY_CHECK_NOT_COMPLETED",
            details={"case_number": case_number}
        )


# Business rule violations
class BusinessRuleViolation(DomainException):
    """Raised when a business rule is violated"""
    
    def __init__(self, rule_name: str, description: str, context: Dict[str, Any]):
        super().__init__(
            f"Business rule violation: {rule_name} - {description}",
            error_code="BUSINESS_RULE_VIOLATION",
            details={"rule_name": rule_name, "context": context}
        )
        self.rule_name = rule_name


# Authorization exceptions
class AuthorizationException(DomainException):
    """Base exception for authorization errors"""
    pass


class InsufficientPermissions(AuthorizationException):
    """Raised when user lacks required permissions"""
    
    def __init__(self, user_id: int, required_permission: str, operation: str):
        super().__init__(
            f"User #{user_id} lacks permission '{required_permission}' for operation: {operation}",
            error_code="INSUFFICIENT_PERMISSIONS",
            details={
                "user_id": user_id,
                "required_permission": required_permission,
                "operation": operation
            }
        )


# Data validation exceptions
class ValidationException(DomainException):
    """Raised when domain data validation fails"""
    
    def __init__(self, entity_type: str, field_errors: Dict[str, List[str]]):
        errors_summary = ", ".join([f"{field}: {', '.join(errors)}" for field, errors in field_errors.items()])
        super().__init__(
            f"Validation failed for {entity_type}: {errors_summary}",
            error_code="VALIDATION_FAILED",
            details={"entity_type": entity_type, "field_errors": field_errors}
        )
        self.field_errors = field_errors