# DENTAL LAB DDD - Domain Models
# Pure Python business logic - NO external dependencies

from datetime import datetime, timedelta
from typing import List, Optional, Set, Dict, Any
from decimal import Decimal
from enum import Enum
from dataclasses import dataclass, field


# Enums - Domain Value Types
class CaseStatus(Enum):
    """Business states for a dental case"""
    PENDING_ACCEPTANCE = "pending_acceptance"
    ON_HOLD = "on_hold"
    IN_PROGRESS = "in_progress"
    QUALITY_CHECK = "quality_check"
    REVISION_NEEDED = "revision_needed"
    READY_TO_SHIP = "ready_to_ship"
    SHIPPED = "shipped"
    DELIVERED = "delivered"
    COMPLETED = "completed"
    CANCELLED = "cancelled"


class TaskStatus(Enum):
    """Task lifecycle states"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    PAUSED = "paused"
    COMPLETED = "completed"
    DELAYED = "delayed"
    BLOCKED = "blocked"
    CANCELLED = "cancelled"
    REVIEW = "review"


class Priority(Enum):
    """Priority levels for cases and tasks"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4


# Value Objects - Immutable business concepts
@dataclass(frozen=True)
class Money:
    """Value object for monetary amounts"""
    amount: Decimal
    currency: str = "EUR"
    
    def __post_init__(self):
        if self.amount < 0:
            raise ValueError("Money amount cannot be negative")
        if not self.currency or len(self.currency) != 3:
            raise ValueError("Currency must be a 3-letter code")
    
    def __add__(self, other: 'Money') -> 'Money':
        if self.currency != other.currency:
            raise ValueError(f"Cannot add {self.currency} to {other.currency}")
        return Money(self.amount + other.amount, self.currency)
    
    def __sub__(self, other: 'Money') -> 'Money':
        if self.currency != other.currency:
            raise ValueError(f"Cannot subtract {other.currency} from {self.currency}")
        return Money(self.amount - other.amount, self.currency)


@dataclass(frozen=True)
class ContactInfo:
    """Value object for contact information"""
    phone: Optional[str] = None
    email: Optional[str] = None
    address: Optional[str] = None
    
    def __post_init__(self):
        if self.email and "@" not in self.email:
            raise ValueError("Invalid email format")


@dataclass(frozen=True)
class PersonName:
    """Value object for person names"""
    first_name: str
    last_name: str
    
    def __post_init__(self):
        if not self.first_name.strip():
            raise ValueError("First name cannot be empty")
    
    @property
    def full_name(self) -> str:
        return f"{self.first_name} {self.last_name}".strip()


# Domain Entities - Objects with identity and lifecycle
class Patient:
    """Patient entity - represents a dental patient"""
    
    def __init__(
        self,
        patient_id: int,
        name: PersonName,
        dentist_id: int,
        contact: ContactInfo = None,
        date_of_birth: Optional[datetime] = None
    ):
        self.patient_id = patient_id
        self.name = name
        self.dentist_id = dentist_id
        self.contact = contact or ContactInfo()
        self.date_of_birth = date_of_birth
        self.events: List = []  # Domain events
    
    def __eq__(self, other) -> bool:
        if not isinstance(other, Patient):
            return False
        return self.patient_id == other.patient_id
    
    def __hash__(self) -> int:
        return hash(self.patient_id)
    
    @property
    def age(self) -> Optional[int]:
        """Calculate patient age"""
        if not self.date_of_birth:
            return None
        today = datetime.now().date()
        birth_date = self.date_of_birth.date() if isinstance(self.date_of_birth, datetime) else self.date_of_birth
        age = today.year - birth_date.year
        if (today.month, today.day) < (birth_date.month, birth_date.day):
            age -= 1
        return age
    
    def update_contact_info(self, contact: ContactInfo) -> None:
        """Update patient contact information"""
        self.contact = contact
        # Could emit PatientContactUpdated event here


class Dentist:
    """Dentist entity - represents a dental professional"""
    
    def __init__(
        self,
        dentist_id: int,
        name: PersonName,
        clinic_name: str,
        contact: ContactInfo,
        user_id: Optional[int] = None
    ):
        self.dentist_id = dentist_id
        self.name = name
        self.clinic_name = clinic_name
        self.contact = contact
        self.user_id = user_id  # Link to user account
        self.events: List = []
    
    def __eq__(self, other) -> bool:
        if not isinstance(other, Dentist):
            return False
        return self.dentist_id == other.dentist_id
    
    def __hash__(self) -> int:
        return hash(self.dentist_id)


class CaseItem:
    """Case item entity - represents an item within a dental case"""
    
    def __init__(
        self,
        case_number: int,
        item_id: int,
        quantity: int,
        unit_id: int,
        estimated_time: timedelta = None,
        notes: str = ""
    ):
        if quantity <= 0:
            raise ValueError("Quantity must be positive")
        
        self.case_number = case_number
        self.item_id = item_id
        self.quantity = quantity
        self.unit_id = unit_id
        self.estimated_time = estimated_time or timedelta(hours=1)
        self.actual_time: Optional[timedelta] = None
        self.status = TaskStatus.PENDING
        self.notes = notes
        self.events: List = []
    
    def start_production(self) -> None:
        """Start production of this item"""
        if self.status != TaskStatus.PENDING:
            raise ValueError(f"Cannot start production: item is {self.status.value}")
        self.status = TaskStatus.IN_PROGRESS
    
    def complete_production(self, actual_time: timedelta) -> None:
        """Complete production of this item"""
        if self.status != TaskStatus.IN_PROGRESS:
            raise ValueError(f"Cannot complete: item is {self.status.value}")
        self.status = TaskStatus.COMPLETED
        self.actual_time = actual_time
    
    @property
    def is_delayed(self) -> bool:
        """Check if production is delayed"""
        return (self.actual_time is not None and 
                self.actual_time > self.estimated_time)
    
    @property
    def total_estimated_time(self) -> timedelta:
        """Total estimated time for this quantity"""
        return self.estimated_time * self.quantity


class Task:
    """Task entity - represents a specific task within a case"""
    
    def __init__(
        self,
        task_id: int,
        case_number: int,
        title: str,
        estimated_duration: timedelta,
        assigned_to: Optional[int] = None,
        case_item_id: Optional[int] = None,
        workflow_stage_id: Optional[int] = None
    ):
        self.task_id = task_id
        self.case_number = case_number
        self.title = title
        self.estimated_duration = estimated_duration
        self.assigned_to = assigned_to
        self.case_item_id = case_item_id
        self.workflow_stage_id = workflow_stage_id
        
        # State
        self.status = TaskStatus.PENDING
        self.priority = Priority.NORMAL
        self.progress_percentage: Decimal = Decimal('0')
        
        # Time tracking
        self.actual_start_time: Optional[datetime] = None
        self.actual_end_time: Optional[datetime] = None
        self.actual_duration: Optional[timedelta] = None
        self.paused_time: timedelta = timedelta()
        
        # Dependencies and blocking
        self.dependencies: Set[int] = set()  # Task IDs this task depends on
        self.blocking_issues: str = ""
        
        # Quality
        self.quality_check_passed: Optional[bool] = None
        self.review_comments: str = ""
        
        self.events: List = []
    
    def can_start(self) -> bool:
        """Check if task can be started (all dependencies completed)"""
        return self.status == TaskStatus.PENDING
        # In real implementation, would check if dependencies are completed
    
    def start_task(self) -> None:
        """Start the task"""
        if not self.can_start():
            raise ValueError("Task cannot be started")
        
        self.status = TaskStatus.IN_PROGRESS
        self.actual_start_time = datetime.now()
    
    def complete_task(self) -> None:
        """Complete the task"""
        if self.status != TaskStatus.IN_PROGRESS:
            raise ValueError("Task must be in progress to complete")
        
        self.status = TaskStatus.COMPLETED
        self.actual_end_time = datetime.now()
        self.progress_percentage = Decimal('100')
        
        if self.actual_start_time:
            self.actual_duration = self.actual_end_time - self.actual_start_time - self.paused_time
    
    def pause_task(self, reason: str = "") -> None:
        """Pause the task"""
        if self.status != TaskStatus.IN_PROGRESS:
            raise ValueError("Can only pause tasks in progress")
        
        self.status = TaskStatus.PAUSED
        if reason:
            self.blocking_issues = reason
    
    def block_task(self, reason: str) -> None:
        """Block the task due to issues"""
        self.status = TaskStatus.BLOCKED
        self.blocking_issues = reason
    
    @property
    def is_overdue(self) -> bool:
        """Check if task is overdue based on estimated duration"""
        if self.status in [TaskStatus.COMPLETED, TaskStatus.CANCELLED]:
            return False
        
        if not self.actual_start_time:
            return False
        
        expected_end = self.actual_start_time + self.estimated_duration
        return datetime.now() > expected_end

class Case:
    """
    Case entity - The core business entity representing a dental case
    This is the aggregate root for the case subdomain
    """
    
    def __init__(
        self,
        case_number: int,
        dentist_id: int,
        patient_id: Optional[int] = None,
        status: CaseStatus = CaseStatus.PENDING_ACCEPTANCE,
        deadline: Optional[datetime] = None,
        created_at: Optional[datetime] = None
    ):
        # Identity
        self.case_number = case_number
        
        # Relationships
        self.dentist_id = dentist_id
        self.patient_id = patient_id
        
        # State
        self.status = status
        self.priority = Priority.NORMAL
        
        # Timing
        self.deadline = deadline
        self.created_at = created_at or datetime.now()
        self.received_date_time: Optional[datetime] = None
        self.estimated_completion: Optional[datetime] = None
        self.actual_completion: Optional[datetime] = None
        self.ship_date_time: Optional[datetime] = None
        self.delivery_date: Optional[datetime] = None
        
        # Progress tracking
        self.progress_percentage: Decimal = Decimal('0')
        self.revision_count: int = 0
        
        # Collections
        self.items: List[CaseItem] = []
        self.tasks: List[Task] = []
        self.selected_teeth: Set[int] = set()  # Tooth numbers
        
        # Workflow
        self.workflow_template_id: Optional[int] = None
        self.current_stage_id: Optional[int] = None
        self.responsible_department_id: Optional[int] = None
        self.assigned_technician_ids: Set[int] = set()
        
        # Quality and feedback
        self.quality_checks: Dict[str, Any] = {}
        self.patient_feedback: Optional[Decimal] = None
        self.dentist_feedback: Optional[Decimal] = None
        
        # Costs
        self.cost_estimate: Optional[Money] = None
        
        # Additional info
        self.notes: str = ""
        self.special_requirements: str = ""
        self.teeth_color: str = ""
        self.delay_reason: str = ""
        
        # Domain events - crucial for DDD
        self.events: List = []
    
    def __eq__(self, other) -> bool:
        if not isinstance(other, Case):
            return False
        return self.case_number == other.case_number
    
    def __hash__(self) -> int:
        return hash(self.case_number)
    
    # Business Logic Methods
    
    def add_item(self, item_id: int, quantity: int, unit_id: int, estimated_time: timedelta = None) -> None:
        """Add an item to the case"""
        # Business rule: Cannot add items to completed/cancelled cases
        if self.status in [CaseStatus.COMPLETED, CaseStatus.CANCELLED]:
            raise ValueError(f"Cannot add items to {self.status.value} case")
        
        # Check if item already exists
        existing_item = next(
            (item for item in self.items if item.item_id == item_id), 
            None
        )
        
        if existing_item:
            # Update quantity instead of adding duplicate
            existing_item.quantity += quantity
        else:
            case_item = CaseItem(
                case_number=self.case_number,
                item_id=item_id,
                quantity=quantity,
                unit_id=unit_id,
                estimated_time=estimated_time
            )
            self.items.append(case_item)
        
        # Emit domain event
        from .events import ItemAddedToCase
        self.events.append(ItemAddedToCase(
            case_number=self.case_number,
            item_id=item_id,
            quantity=quantity,
            unit_id=unit_id,
            occurred_at=datetime.now()
        ))
        
        # Recalculate progress
        self._recalculate_progress()
    
    def remove_item(self, item_id: int) -> None:
        """Remove an item from the case"""
        if self.status in [CaseStatus.COMPLETED, CaseStatus.CANCELLED]:
            raise ValueError(f"Cannot remove items from {self.status.value} case")
        
        self.items = [item for item in self.items if item.item_id != item_id]
        self._recalculate_progress()
    
    def add_task(self, task: Task) -> None:
        """Add a task to the case"""
        if task.case_number != self.case_number:
            raise ValueError("Task case_number must match this case")
        
        self.tasks.append(task)
    
    def select_teeth(self, tooth_numbers: List[int], color: str = "") -> None:
        """Select teeth for this case"""
        for tooth_num in tooth_numbers:
            if not (1 <= tooth_num <= 32):  # Standard dental numbering
                raise ValueError(f"Invalid tooth number: {tooth_num}")
        
        self.selected_teeth.update(tooth_numbers)
        if color:
            self.teeth_color = color
    
    def set_deadline(self, deadline: datetime) -> None:
        """Set the case deadline with business rules"""
        if deadline <= datetime.now():
            raise ValueError("Deadline cannot be in the past")
        
        if self.status == CaseStatus.COMPLETED:
            raise ValueError("Cannot change deadline of completed case")
        
        self.deadline = deadline
    
    def start_case(self) -> None:
        """Start the case (move from pending to in progress)"""
        if self.status != CaseStatus.PENDING_ACCEPTANCE:
            raise ValueError(f"Cannot start case with status {self.status.value}")
        
        if not self.items:
            raise ValueError("Cannot start case without items")
        
        self.status = CaseStatus.IN_PROGRESS
        self.received_date_time = datetime.now()
        
        # Emit domain event
        from .events import CaseStarted
        self.events.append(CaseStarted(
            case_number=self.case_number,
            started_at=self.received_date_time,
            occurred_at=datetime.now()
        ))
    
    def complete_case(self) -> None:
        """Complete the case"""
        if self.status != CaseStatus.READY_TO_SHIP:
            raise ValueError("Case must be ready to ship before completion")
        
        # Business rule: All items must be completed
        incomplete_items = [item for item in self.items if item.status != TaskStatus.COMPLETED]
        if incomplete_items:
            raise ValueError("All items must be completed before case completion")
        
        self.status = CaseStatus.COMPLETED
        self.actual_completion = datetime.now()
        self.progress_percentage = Decimal('100')
        
        # Emit domain event
        from .events import CaseCompleted
        self.events.append(CaseCompleted(
            case_number=self.case_number,
            completed_at=self.actual_completion,
            occurred_at=datetime.now()
        ))
    
    def ship_case(self, ship_date: datetime = None) -> None:
        """Mark case as shipped"""
        if self.status != CaseStatus.READY_TO_SHIP:
            raise ValueError("Case must be ready to ship")
        
        self.status = CaseStatus.SHIPPED
        self.ship_date_time = ship_date or datetime.now()
    
    def deliver_case(self, delivery_date: datetime = None) -> None:
        """Mark case as delivered"""
        if self.status != CaseStatus.SHIPPED:
            raise ValueError("Case must be shipped before delivery")
        
        self.status = CaseStatus.DELIVERED
        self.delivery_date = delivery_date or datetime.now()
    
    def cancel_case(self, reason: str = "") -> None:
        """Cancel the case"""
        if self.status in [CaseStatus.COMPLETED, CaseStatus.DELIVERED]:
            raise ValueError(f"Cannot cancel {self.status.value} case")
        
        self.status = CaseStatus.CANCELLED
        if reason:
            self.delay_reason = reason
    
    def require_revision(self, reason: str) -> None:
        """Mark case as needing revision"""
        if self.status not in [CaseStatus.QUALITY_CHECK, CaseStatus.READY_TO_SHIP]:
            raise ValueError("Can only require revision during quality check")
        
        self.status = CaseStatus.REVISION_NEEDED
        self.revision_count += 1
        self.delay_reason = reason
    
    def put_on_hold(self, reason: str) -> None:
        """Put case on hold"""
        if self.status in [CaseStatus.COMPLETED, CaseStatus.CANCELLED, CaseStatus.DELIVERED]:
            raise ValueError(f"Cannot put {self.status.value} case on hold")
        
        self.status = CaseStatus.ON_HOLD
        self.delay_reason = reason
    
    # Business Logic Queries
    
    def is_overdue(self) -> bool:
        """Check if case is overdue"""
        if not self.deadline:
            return False
        
        if self.status in [CaseStatus.COMPLETED, CaseStatus.CANCELLED, CaseStatus.DELIVERED]:
            return False
        
        return datetime.now() > self.deadline
    
    def is_delayed(self) -> bool:
        """Check if case is delayed compared to estimate"""
        if not self.estimated_completion:
            return False
        
        if self.status in [CaseStatus.COMPLETED, CaseStatus.CANCELLED, CaseStatus.DELIVERED]:
            return False
        
        return datetime.now() > self.estimated_completion
    
    def days_until_deadline(self) -> Optional[int]:
        """Calculate days until deadline"""
        if not self.deadline:
            return None
        
        delta = self.deadline - datetime.now()
        return delta.days
    
    def get_total_estimated_time(self) -> timedelta:
        """Calculate total estimated time for all items"""
        return sum(
            (item.total_estimated_time for item in self.items),
            timedelta()
        )
    
    def get_total_cost_estimate(self) -> Optional[Money]:
        """Calculate total cost estimate"""
        # This would integrate with Item pricing from other bounded contexts
        return self.cost_estimate
    
    def can_be_shipped(self) -> bool:
        """Business rule: determine if case can be shipped"""
        return (
            self.status == CaseStatus.READY_TO_SHIP and
            all(item.status == TaskStatus.COMPLETED for item in self.items) and
            all(task.status == TaskStatus.COMPLETED for task in self.tasks if task.task_id)
        )
    
    def get_completion_percentage(self) -> Decimal:
        """Calculate completion percentage based on items and tasks"""
        if not self.items and not self.tasks:
            return Decimal('0')
        
        total_items = len(self.items)
        completed_items = len([item for item in self.items if item.status == TaskStatus.COMPLETED])
        
        if total_items > 0:
            return Decimal(completed_items * 100 / total_items).quantize(Decimal('0.01'))
        
        return Decimal('0')
    
    # Private Methods
    
    def _recalculate_progress(self) -> None:
        """Recalculate progress percentage"""
        self.progress_percentage = self.get_completion_percentage()
    
    def _calculate_estimated_completion(self) -> None:
        """Calculate estimated completion date based on items and workflow"""
        if self.received_date_time:
            total_time = self.get_total_estimated_time()
            self.estimated_completion = self.received_date_time + total_time