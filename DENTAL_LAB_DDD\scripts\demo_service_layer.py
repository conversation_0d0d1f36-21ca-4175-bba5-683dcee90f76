# DENTAL LAB DDD - Simple Service Layer Demo
# Demo without Django dependencies - using only domain and fake repositories

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from datetime import datetime, timedelta
from decimal import Decimal

# Import only what we need without Django
from dental_lab.domain import model, exceptions


# Simple fake repositories for demo
class FakeRepository:
    """Base fake repository"""
    
    def __init__(self):
        self._entities = {}
        self._next_id = 1
    
    def add(self, entity):
        entity_id = getattr(entity, self._id_field)
        if entity_id is None:
            entity_id = self._next_id
            setattr(entity, self._id_field, entity_id)
            self._next_id += 1
        
        self._entities[entity_id] = entity
        return entity
    
    def get(self, entity_id):
        return self._entities.get(entity_id)
    
    def list_all(self):
        return list(self._entities.values())


class SimpleFakeCaseRepository(FakeRepository):
    def __init__(self):
        super().__init__()
        self._id_field = 'case_number'
        self._next_id = 1000
    
    def get_next_case_number(self):
        if self._entities:
            return max(self._entities.keys()) + 1
        return self._next_id


class SimpleFakePatientRepository(FakeRepository):
    def __init__(self):
        super().__init__()
        self._id_field = 'patient_id'


class SimpleFakeDentistRepository(FakeRepository):
    def __init__(self):
        super().__init__()
        self._id_field = 'dentist_id'


class SimpleFakeUnitOfWork:
    def __init__(self):
        self.cases = SimpleFakeCaseRepository()
        self.patients = SimpleFakePatientRepository()
        self.dentists = SimpleFakeDentistRepository()
        self.committed = False
    
    def __enter__(self):
        return self
    
    def __exit__(self, *args):
        pass
    
    def commit(self):
        self.committed = True


# Simplified use cases for demo
def create_new_case(dentist_id, patient_id=None, items=None, deadline=None, priority=2, notes="", uow=None):
    """Create a new dental case"""
    with uow:
        # Verify dentist exists
        dentist = uow.dentists.get(dentist_id)
        if not dentist:
            raise exceptions.DentistNotFound(dentist_id)
        
        # Verify patient if provided
        if patient_id:
            patient = uow.patients.get(patient_id)
            if not patient:
                raise exceptions.PatientNotFound(patient_id)
        
        # Generate next case number
        next_case_number = uow.cases.get_next_case_number()
        
        # Create the case
        case = model.Case(
            case_number=next_case_number,
            dentist_id=dentist_id,
            patient_id=patient_id,
            deadline=deadline
        )
        
        # Set additional properties
        case.priority = model.Priority(priority)
        case.notes = notes
        
        # Add items if provided
        if items:
            for item_data in items:
                case.add_item(
                    item_id=item_data['item_id'],
                    quantity=item_data['quantity'],
                    unit_id=item_data['unit_id'],
                    estimated_time=item_data.get('estimated_time')
                )
        
        # Save the case
        uow.cases.add(case)
        uow.commit()
        
        return case.case_number


def register_dentist(first_name, last_name, clinic_name, phone_number, email=None, uow=None):
    """Register a new dentist"""
    with uow:
        # Generate dentist ID
        existing_dentists = uow.dentists.list_all()
        next_dentist_id = max([d.dentist_id for d in existing_dentists], default=99) + 1
        
        # Create dentist
        name = model.PersonName(first_name, last_name)
        contact = model.ContactInfo(phone=phone_number, email=email)
        
        dentist = model.Dentist(
            dentist_id=next_dentist_id,
            name=name,
            clinic_name=clinic_name,
            contact=contact
        )
        
        # Save dentist
        uow.dentists.add(dentist)
        uow.commit()
        
        return dentist.dentist_id


def register_patient(dentist_id, first_name, last_name, phone_number=None, email=None, uow=None):
    """Register a new patient"""
    with uow:
        # Verify dentist exists
        dentist = uow.dentists.get(dentist_id)
        if not dentist:
            raise exceptions.DentistNotFound(dentist_id)
        
        # Generate patient ID
        existing_patients = [p for p in uow.patients.list_all() if p.dentist_id == dentist_id]
        next_patient_id = max([p.patient_id for p in existing_patients], default=0) + 1
        
        # Create patient
        name = model.PersonName(first_name, last_name)
        contact = model.ContactInfo(phone=phone_number, email=email)
        
        patient = model.Patient(
            patient_id=next_patient_id,
            name=name,
            dentist_id=dentist_id,
            contact=contact
        )
        
        # Save patient
        uow.patients.add(patient)
        uow.commit()
        
        return patient.patient_id


def get_case_details(case_number, uow):
    """Get case details"""
    with uow:
        case = uow.cases.get(case_number)
        if not case:
            raise exceptions.CaseNotFound(case_number)
        return case


def start_case(case_number, uow):
    """Start a case"""
    with uow:
        case = uow.cases.get(case_number)
        if not case:
            raise exceptions.CaseNotFound(case_number)
        
        case.start_case()
        uow.commit()


def demo_case_creation_workflow():
    """
    Demonstrate a complete case creation and workflow
    """
    print("DENTAL LAB DDD - USE CASES DEMO")
    print("=" * 50)
    
    # Use fake UoW for demonstration
    fake_uow = SimpleFakeUnitOfWork()
    
    # Step 1: Register a dentist
    print("\n1. Registering Dentist...")
    dentist_id = register_dentist(
        first_name="Dr. John",
        last_name="Smith",
        clinic_name="SmileCenter Clinic",
        phone_number="+355-69-123-4567",
        email="<EMAIL>",
        uow=fake_uow
    )
    print(f"SUCCESS: Dentist registered with ID: {dentist_id}")
    
    # Step 2: Register a patient
    print("\n2. Registering Patient...")
    patient_id = register_patient(
        dentist_id=dentist_id,
        first_name="Maria",
        last_name="Rossi",
        phone_number="+355-69-987-6543",
        email="<EMAIL>",
        uow=fake_uow
    )
    print(f"SUCCESS: Patient registered with ID: {patient_id}")
    
    # Step 3: Create a case
    print("\n3. Creating New Case...")
    case_number = create_new_case(
        dentist_id=dentist_id,
        patient_id=patient_id,
        items=[
            {
                'item_id': 101,
                'quantity': 2,
                'unit_id': 1,
                'estimated_time': timedelta(hours=4)
            },
            {
                'item_id': 102,
                'quantity': 1,
                'unit_id': 2,
                'estimated_time': timedelta(hours=2)
            }
        ],
        deadline=datetime.now() + timedelta(days=7),
        priority=3,  # High priority
        notes="Urgent case for front teeth",
        uow=fake_uow
    )
    print(f"SUCCESS: Case created with number: {case_number}")
    
    # Step 4: Get case details
    print("\n4. Retrieving Case Details...")
    case = get_case_details(case_number, fake_uow)
    print(f"INFO: Case Status: {case.status.value}")
    print(f"INFO: Items Count: {len(case.items)}")
    print(f"INFO: Priority: {case.priority.value}")
    
    # Step 5: Start the case
    print("\n5. Starting Case Production...")
    start_case(case_number, fake_uow)
    
    updated_case = get_case_details(case_number, fake_uow)
    print(f"SUCCESS: Case status updated to: {updated_case.status.value}")
    
    # Step 6: Demonstrate domain logic
    print("\n6. Demonstrating Domain Logic...")
    
    # Start item production
    item_101 = next(item for item in case.items if item.item_id == 101)
    item_101.start_production()
    print(f"SUCCESS: Started production for item 101")
    
    # Complete item production
    item_101.complete_production(timedelta(hours=3, minutes=45))
    print(f"SUCCESS: Completed production for item 101 in {item_101.actual_time}")
    
    # Complete second item
    item_102 = next(item for item in case.items if item.item_id == 102)
    item_102.start_production()
    item_102.complete_production(timedelta(hours=1, minutes=30))
    print(f"SUCCESS: Completed production for item 102 in {item_102.actual_time}")
    
    # Step 7: Quality control and completion
    print("\n7. Quality Control & Completion...")
    
    # Move case to ready for shipping
    case.status = model.CaseStatus.READY_TO_SHIP
    print(f"SUCCESS: Case moved to: {case.status.value}")
    
    # Complete the case
    case.complete_case()
    print(f"SUCCESS: Case completed with status: {case.status.value}")
    
    # Step 8: Show business logic results
    print("\n8. Business Logic Results...")
    print(f"METRICS: Case Progress: {case.get_completion_percentage()}%")
    print(f"METRICS: Total Estimated Time: {case.get_total_estimated_time()}")
    print(f"METRICS: Is Overdue: {case.is_overdue()}")
    print(f"METRICS: Can Be Shipped: {case.can_be_shipped()}")
    
    print("\nDEMO COMPLETED SUCCESSFULLY!")
    print("=" * 50)
    
    return {
        'dentist_id': dentist_id,
        'patient_id': patient_id,
        'case_number': case_number,
        'final_status': case.status.value
    }


def demo_business_rules_validation():
    """
    Demonstrate business rules and error handling
    """
    print("\nBUSINESS RULES VALIDATION DEMO")
    print("=" * 40)
    
    fake_uow = SimpleFakeUnitOfWork()
    
    # Try to create case with non-existent dentist
    try:
        create_new_case(
            dentist_id=999,  # Non-existent
            uow=fake_uow
        )
    except Exception as e:
        print(f"ERROR (Expected): {type(e).__name__}: {str(e)}")
    
    # Register dentist for further tests
    dentist_id = register_dentist(
        first_name="Dr. Jane",
        last_name="Doe",
        clinic_name="Test Clinic",
        phone_number="+355-69-000-0000",
        uow=fake_uow
    )
    
    # Create a case without items
    case_number = create_new_case(
        dentist_id=dentist_id,
        deadline=datetime.now() + timedelta(days=5),
        uow=fake_uow
    )
    
    # Try to start case without items
    try:
        start_case(case_number, fake_uow)
    except Exception as e:
        print(f"ERROR (Expected): {type(e).__name__}: {str(e)}")
    
    # Test domain model business rules directly
    case = get_case_details(case_number, fake_uow)
    
    # Add an item
    case.add_item(item_id=201, quantity=1, unit_id=1)
    print(f"SUCCESS: Added item to case")
    
    # Now start should work
    case.start_case()
    print(f"SUCCESS: Case started after adding items")
    
    # Try to complete case before items are done
    try:
        case.complete_case()
    except Exception as e:
        print(f"ERROR (Expected): {type(e).__name__}: {str(e)}")
    
    # Complete items and then case
    for item in case.items:
        item.start_production()
        item.complete_production(timedelta(hours=2))
    
    case.status = model.CaseStatus.READY_TO_SHIP
    case.complete_case()
    print(f"SUCCESS: Case completed after all items finished: {case.status.value}")
    
    print("SUCCESS: Business rules validation working correctly!")


if __name__ == "__main__":
    # Run the demos
    result = demo_case_creation_workflow()
    demo_business_rules_validation()
    
    print(f"\nDEMO RESULTS:")
    print(f"   Dentist: {result['dentist_id']}")
    print(f"   Patient: {result['patient_id']}")  
    print(f"   Case: {result['case_number']}")
    print(f"   Status: {result['final_status']}")
