# DENTAL LAB DDD - Unit Tests for Service Layer
# Fast tests for use cases using fake repositories

import pytest
from datetime import datetime, timedelta
from decimal import Decimal

from dental_lab.domain import model, exceptions
from dental_lab.service_layer import services, uow


class TestCaseManagementServices:
    """Test case management use cases"""
    
    def test_create_new_case_success(self):
        """Test successful case creation"""
        fake_uow = uow.FakeUnitOfWork()
        
        # Setup test data
        dentist = model.Dentist(
            100, 
            model.PersonName("Dr. <PERSON>", "<PERSON>"), 
            "Test Clinic", 
            model.ContactInfo()
        )
        patient = model.Patient(
            1, 
            model.PersonName("<PERSON>", "<PERSON><PERSON>"), 
            100, 
            model.ContactInfo()
        )
        
        fake_uow.dentists.add(dentist)
        fake_uow.patients.add(patient)
        
        # Create case
        case_number = services.create_new_case(
            dentist_id=100,
            patient_id=1,
            deadline=datetime.now() + timedelta(days=7),
            notes="Test case",
            uow=fake_uow
        )
        
        # Verify results
        assert case_number == 1000  # First case number
        assert fake_uow.committed
        
        # Verify case was created
        created_case = fake_uow.cases.get_by_case_number(case_number)
        assert created_case is not None
        assert created_case.dentist_id == 100
        assert created_case.patient_id == 1
        assert created_case.notes == "Test case"
    
    def test_create_case_with_items(self):
        """Test creating case with items"""
        fake_uow = uow.FakeUnitOfWork()
        
        # Setup test data
        dentist = model.Dentist(100, model.PersonName("Dr. Test", "Dentist"), "Clinic", model.ContactInfo())
        fake_uow.dentists.add(dentist)
        
        # Items to add
        items = [
            {'item_id': 50, 'quantity': 2, 'unit_id': 1, 'estimated_time': timedelta(hours=3)},
            {'item_id': 51, 'quantity': 1, 'unit_id': 1, 'estimated_time': timedelta(hours=1)}
        ]
        
        # Create case with items
        case_number = services.create_new_case(
            dentist_id=100,
            items=items,
            uow=fake_uow
        )
        
        # Verify case and items
        created_case = fake_uow.cases.get_by_case_number(case_number)
        assert len(created_case.items) == 2
        assert created_case.items[0].item_id == 50
        assert created_case.items[0].quantity == 2
        assert created_case.items[1].item_id == 51
        assert created_case.items[1].quantity == 1
    
    def test_create_case_dentist_not_found(self):
        """Test case creation with non-existent dentist"""
        fake_uow = uow.FakeUnitOfWork()
        
        with pytest.raises(exceptions.DentistNotFound):
            services.create_new_case(
                dentist_id=999,  # Non-existent dentist
                uow=fake_uow
            )
    
    def test_create_case_patient_not_found(self):
        """Test case creation with non-existent patient"""
        fake_uow = uow.FakeUnitOfWork()
        
        # Setup dentist but not patient
        dentist = model.Dentist(100, model.PersonName("Dr. Test", "Dentist"), "Clinic", model.ContactInfo())
        fake_uow.dentists.add(dentist)
        
        with pytest.raises(exceptions.PatientNotFound):
            services.create_new_case(
                dentist_id=100,
                patient_id=999,  # Non-existent patient
                uow=fake_uow
            )
    
    def test_create_case_patient_dentist_mismatch(self):
        """Test case creation with patient belonging to different dentist"""
        fake_uow = uow.FakeUnitOfWork()
        
        # Setup dentists and patient
        dentist1 = model.Dentist(100, model.PersonName("Dr. One", "Dentist"), "Clinic 1", model.ContactInfo())
        dentist2 = model.Dentist(200, model.PersonName("Dr. Two", "Dentist"), "Clinic 2", model.ContactInfo())
        patient = model.Patient(1, model.PersonName("Patient", "One"), 200)  # Belongs to dentist 200
        
        fake_uow.dentists.add(dentist1)
        fake_uow.dentists.add(dentist2)
        fake_uow.patients.add(patient)
        
        with pytest.raises(exceptions.PatientDentistMismatch):
            services.create_new_case(
                dentist_id=100,  # Trying to create case for dentist 100
                patient_id=1,    # But patient belongs to dentist 200
                uow=fake_uow
            )
    
    def test_get_case_details_success(self):
        """Test retrieving case details"""
        fake_uow = uow.FakeUnitOfWork()
        
        # Create test case
        dentist = model.Dentist(100, model.PersonName("Dr. Test", "Dentist"), "Clinic", model.ContactInfo())
        fake_uow.dentists.add(dentist)
        
        case_number = services.create_new_case(
            dentist_id=100,
            notes="Test case for retrieval",
            uow=fake_uow
        )
        
        # Retrieve case details
        case_details = services.get_case_details(case_number, fake_uow)
        
        # Verify details
        assert case_details.case_number == case_number
        assert case_details.dentist_id == 100
        assert case_details.notes == "Test case for retrieval"
    
    def test_get_case_details_not_found(self):
        """Test retrieving non-existent case"""
        fake_uow = uow.FakeUnitOfWork()
        
        with pytest.raises(exceptions.CaseNotFound):
            services.get_case_details(9999, fake_uow)
    
    def test_start_case_success(self):
        """Test starting a case"""
        fake_uow = uow.FakeUnitOfWork()
        
        # Create case with items
        dentist = model.Dentist(100, model.PersonName("Dr. Test", "Dentist"), "Clinic", model.ContactInfo())
        fake_uow.dentists.add(dentist)
        
        items = [{'item_id': 50, 'quantity': 1, 'unit_id': 1}]
        case_number = services.create_new_case(
            dentist_id=100,
            items=items,
            uow=fake_uow
        )
        
        # Start the case
        services.start_case(case_number, started_by=500, uow=fake_uow)
        
        # Verify case status
        started_case = fake_uow.cases.get_by_case_number(case_number)
        assert started_case.status == model.CaseStatus.IN_PROGRESS
        assert started_case.received_date_time is not None
    
    def test_start_case_without_items(self):
        """Test starting case without items should fail"""
        fake_uow = uow.FakeUnitOfWork()
        
        # Create case without items
        dentist = model.Dentist(100, model.PersonName("Dr. Test", "Dentist"), "Clinic", model.ContactInfo())
        fake_uow.dentists.add(dentist)
        
        case_number = services.create_new_case(dentist_id=100, uow=fake_uow)
        
        with pytest.raises(exceptions.CaseHasNoItems):
            services.start_case(case_number, uow=fake_uow)
    
    def test_complete_case_success(self):
        """Test completing a case"""
        fake_uow = uow.FakeUnitOfWork()
        
        # Create and start case
        dentist = model.Dentist(100, model.PersonName("Dr. Test", "Dentist"), "Clinic", model.ContactInfo())
        fake_uow.dentists.add(dentist)
        
        items = [{'item_id': 50, 'quantity': 1, 'unit_id': 1}]
        case_number = services.create_new_case(dentist_id=100, items=items, uow=fake_uow)
        services.start_case(case_number, uow=fake_uow)
        
        # Complete the case items first
        case = fake_uow.cases.get_by_case_number(case_number)
        for item in case.items:
            item.complete_production(timedelta(hours=2))
        fake_uow.cases.update(case)
        
        # Move case to ready to ship
        case.status = model.CaseStatus.READY_TO_SHIP
        fake_uow.cases.update(case)
        
        # Complete the case
        services.complete_case(case_number, completed_by=500, completion_notes="All done!", uow=fake_uow)
        
        # Verify completion
        completed_case = fake_uow.cases.get_by_case_number(case_number)
        assert completed_case.status == model.CaseStatus.COMPLETED
        assert completed_case.actual_completion is not None
        assert "All done!" in completed_case.notes
    
    def test_add_item_to_case_success(self):
        """Test adding item to existing case"""
        fake_uow = uow.FakeUnitOfWork()
        
        # Create case
        dentist = model.Dentist(100, model.PersonName("Dr. Test", "Dentist"), "Clinic", model.ContactInfo())
        fake_uow.dentists.add(dentist)
        
        case_number = services.create_new_case(dentist_id=100, uow=fake_uow)
        
        # Add item to case
        services.add_item_to_case(
            case_number=case_number,
            item_id=50,
            quantity=2,
            unit_id=1,
            estimated_time=timedelta(hours=3),
            special_instructions="Handle with care",
            uow=fake_uow
        )
        
        # Verify item was added
        case = fake_uow.cases.get_by_case_number(case_number)
        assert len(case.items) == 1
        assert case.items[0].item_id == 50
        assert case.items[0].quantity == 2
        assert case.items[0].notes == "Handle with care"
    
    def test_add_item_invalid_quantity(self):
        """Test adding item with invalid quantity"""
        fake_uow = uow.FakeUnitOfWork()
        
        # Create case
        dentist = model.Dentist(100, model.PersonName("Dr. Test", "Dentist"), "Clinic", model.ContactInfo())
        fake_uow.dentists.add(dentist)
        
        case_number = services.create_new_case(dentist_id=100, uow=fake_uow)
        
        # Try to add item with invalid quantity
        with pytest.raises(exceptions.InvalidQuantity):
            services.add_item_to_case(
                case_number=case_number,
                item_id=50,
                quantity=0,  # Invalid quantity
                unit_id=1,
                uow=fake_uow
            )
    
    def test_update_case_deadline_success(self):
        """Test updating case deadline"""
        fake_uow = uow.FakeUnitOfWork()
        
        # Create case
        dentist = model.Dentist(100, model.PersonName("Dr. Test", "Dentist"), "Clinic", model.ContactInfo())
        fake_uow.dentists.add(dentist)
        
        case_number = services.create_new_case(dentist_id=100, uow=fake_uow)
        
        # Update deadline
        new_deadline = datetime.now() + timedelta(days=14)
        services.update_case_deadline(
            case_number=case_number,
            new_deadline=new_deadline,
            reason="Client requested extension",
            uow=fake_uow
        )
        
        # Verify deadline update
        case = fake_uow.cases.get_by_case_number(case_number)
        assert case.deadline == new_deadline
        assert "Client requested extension" in case.notes
    
    def test_cancel_case_success(self):
        """Test cancelling a case"""
        fake_uow = uow.FakeUnitOfWork()
        
        # Create case
        dentist = model.Dentist(100, model.PersonName("Dr. Test", "Dentist"), "Clinic", model.ContactInfo())
        fake_uow.dentists.add(dentist)
        
        case_number = services.create_new_case(dentist_id=100, uow=fake_uow)
        
        # Cancel case
        services.cancel_case(
            case_number=case_number,
            reason="Patient cancelled order",
            cancelled_by=500,
            uow=fake_uow
        )
        
        # Verify cancellation
        case = fake_uow.cases.get_by_case_number(case_number)
        assert case.status == model.CaseStatus.CANCELLED
        assert case.delay_reason == "Patient cancelled order"
    
    def test_list_overdue_cases(self):
        """Test listing overdue cases"""
        fake_uow = uow.FakeUnitOfWork()
        
        # Create dentist
        dentist = model.Dentist(100, model.PersonName("Dr. Test", "Dentist"), "Clinic", model.ContactInfo())
        fake_uow.dentists.add(dentist)
        
        # Create overdue case
        past_deadline = datetime.now() - timedelta(days=1)
        overdue_case_number = services.create_new_case(
            dentist_id=100,
            deadline=past_deadline,
            uow=fake_uow
        )
        
        # Create future case
        future_deadline = datetime.now() + timedelta(days=1)
        future_case_number = services.create_new_case(
            dentist_id=100,
            deadline=future_deadline,
            uow=fake_uow
        )
        
        # List overdue cases
        overdue_cases = services.list_overdue_cases(fake_uow)
        
        # Verify results
        overdue_case_numbers = [case.case_number for case in overdue_cases]
        assert overdue_case_number in overdue_case_numbers
        assert future_case_number not in overdue_case_numbers


class TestPatientManagementServices:
    """Test patient management use cases"""
    
    def test_register_patient_success(self):
        """Test successful patient registration"""
        fake_uow = uow.FakeUnitOfWork()
        
        # Create dentist
        dentist = model.Dentist(100, model.PersonName("Dr. Test", "Dentist"), "Clinic", model.ContactInfo())
        fake_uow.dentists.add(dentist)
        
        # Register patient
        patient_id = services.register_patient(
            dentist_id=100,
            first_name="John",
            last_name="Doe",
            phone_number="************",
            email="<EMAIL>",
            date_of_birth=datetime(1990, 5, 15),
            uow=fake_uow
        )
        
        # Verify patient creation
        assert patient_id == 1
        assert fake_uow.committed
        
        patient = fake_uow.patients.get_by_patient_id(patient_id)
        assert patient is not None
        assert patient.name.full_name == "John Doe"
        assert patient.dentist_id == 100
        assert patient.contact.phone == "************"
    
    def test_register_patient_dentist_not_found(self):
        """Test patient registration with non-existent dentist"""
        fake_uow = uow.FakeUnitOfWork()
        
        with pytest.raises(exceptions.DentistNotFound):
            services.register_patient(
                dentist_id=999,  # Non-existent dentist
                first_name="John",
                last_name="Doe",
                uow=fake_uow
            )
    
    def test_register_patient_invalid_data(self):
        """Test patient registration with invalid data"""
        fake_uow = uow.FakeUnitOfWork()
        
        # Create dentist
        dentist = model.Dentist(100, model.PersonName("Dr. Test", "Dentist"), "Clinic", model.ContactInfo())
        fake_uow.dentists.add(dentist)
        
        with pytest.raises(exceptions.InvalidPatientData):
            services.register_patient(
                dentist_id=100,
                first_name="",  # Empty first name
                last_name="Doe",
                uow=fake_uow
            )
    
    def test_search_patients(self):
        """Test patient search functionality"""
        fake_uow = uow.FakeUnitOfWork()
        
        # Create dentist and patients
        dentist = model.Dentist(100, model.PersonName("Dr. Test", "Dentist"), "Clinic", model.ContactInfo())
        fake_uow.dentists.add(dentist)
        
        services.register_patient(100, "John", "Doe", uow=fake_uow)
        services.register_patient(100, "Jane", "Smith", uow=fake_uow)
        services.register_patient(100, "Bob", "Johnson", uow=fake_uow)
        
        # Search for "John"
        search_results = services.search_patients("John", dentist_id=100, uow=fake_uow)
        
        # Verify search results
        assert len(search_results) == 1
        assert search_results[0].name.first_name == "John"


class TestDentistManagementServices:
    """Test dentist management use cases"""
    
    def test_register_dentist_success(self):
        """Test successful dentist registration"""
        fake_uow = uow.FakeUnitOfWork()
        
        # Register dentist
        dentist_id = services.register_dentist(
            first_name="Dr. Sarah",
            last_name="Wilson",
            clinic_name="Smile Dental",
            phone_number="555-0123",
            email="<EMAIL>",
            uow=fake_uow
        )
        
        # Verify dentist creation
        assert dentist_id == 100  # First dentist
        assert fake_uow.committed
        
        dentist = fake_uow.dentists.get_by_dentist_id(dentist_id)
        assert dentist is not None
        assert dentist.name.full_name == "Dr. Sarah Wilson"
        assert dentist.clinic_name == "Smile Dental"
    
    def test_register_dentist_invalid_data(self):
        """Test dentist registration with invalid data"""
        fake_uow = uow.FakeUnitOfWork()
        
        with pytest.raises(exceptions.InvalidDentistData):
            services.register_dentist(
                first_name="",  # Empty first name
                last_name="Wilson",
                clinic_name="Smile Dental",
                phone_number="555-0123",
                uow=fake_uow
            )


class TestTaskManagementServices:
    """Test task management use cases"""
    
    def test_create_task_success(self):
        """Test successful task creation"""
        fake_uow = uow.FakeUnitOfWork()
        
        # Create case first
        dentist = model.Dentist(100, model.PersonName("Dr. Test", "Dentist"), "Clinic", model.ContactInfo())
        fake_uow.dentists.add(dentist)
        
        case_number = services.create_new_case(dentist_id=100, uow=fake_uow)
        
        # Create task
        task_id = services.create_task(
            case_number=case_number,
            title="Polish crown",
            description="Final polish of the crown",
            estimated_duration=timedelta(hours=2),
            priority=3,
            uow=fake_uow
        )
        
        # Verify task creation
        assert task_id == 1
        assert fake_uow.committed
        
        task = fake_uow.tasks.get_by_task_id(task_id)
        assert task is not None
        assert task.title == "Polish crown"
        assert task.case_number == case_number
        assert task.priority == model.Priority.HIGH
    
    def test_task_lifecycle(self):
        """Test complete task lifecycle"""
        fake_uow = uow.FakeUnitOfWork()
        
        # Create case and task
        dentist = model.Dentist(100, model.PersonName("Dr. Test", "Dentist"), "Clinic", model.ContactInfo())
        fake_uow.dentists.add(dentist)
        
        case_number = services.create_new_case(dentist_id=100, uow=fake_uow)
        task_id = services.create_task(
            case_number=case_number,
            title="Test task",
            description="Test description",
            estimated_duration=timedelta(hours=1),
            uow=fake_uow
        )
        
        # Assign task
        services.assign_task(
            task_id=task_id,
            assigned_to=500,
            assignment_notes="Assigned to technician",
            uow=fake_uow
        )
        
        # Start task
        services.start_task(
            task_id=task_id,
            started_by=500,
            start_notes="Starting work",
            uow=fake_uow
        )
        
        # Complete task
        services.complete_task(
            task_id=task_id,
            completed_by=500,
            completion_notes="Work completed successfully",
            quality_check_passed=True,
            uow=fake_uow
        )
        
        # Verify final state
        task = fake_uow.tasks.get_by_task_id(task_id)
        assert task.assigned_to == 500
        assert task.status == model.TaskStatus.COMPLETED
        assert task.quality_check_passed == True
        assert task.review_comments == "Work completed successfully"
    
    def test_get_pending_tasks(self):
        """Test getting pending tasks"""
        fake_uow = uow.FakeUnitOfWork()
        
        # Create case and tasks
        dentist = model.Dentist(100, model.PersonName("Dr. Test", "Dentist"), "Clinic", model.ContactInfo())
        fake_uow.dentists.add(dentist)
        
        case_number = services.create_new_case(dentist_id=100, uow=fake_uow)
        
        # Create pending task
        pending_task_id = services.create_task(
            case_number=case_number,
            title="Pending task",
            description="This task is pending",
            estimated_duration=timedelta(hours=1),
            uow=fake_uow
        )
        
        # Create completed task
        completed_task_id = services.create_task(
            case_number=case_number,
            title="Completed task",
            description="This task is completed",
            estimated_duration=timedelta(hours=1),
            uow=fake_uow
        )
        
        # Complete the second task
        services.start_task(completed_task_id, uow=fake_uow)
        services.complete_task(completed_task_id, uow=fake_uow)
        
        # Get pending tasks
        pending_tasks = services.get_pending_tasks(uow=fake_uow)
        
        # Verify results
        pending_task_ids = [task.task_id for task in pending_tasks]
        assert pending_task_id in pending_task_ids
        assert completed_task_id not in pending_task_ids


class TestAnalyticsServices:
    """Test analytics and reporting services"""
    
    def test_get_case_statistics(self):
        """Test getting case statistics"""
        fake_uow = uow.FakeUnitOfWork()
        
        # Create dentist
        dentist = model.Dentist(100, model.PersonName("Dr. Test", "Dentist"), "Clinic", model.ContactInfo())
        fake_uow.dentists.add(dentist)
        
        # Create cases with different statuses
        case1 = services.create_new_case(dentist_id=100, uow=fake_uow)  # pending
        case2 = services.create_new_case(dentist_id=100, uow=fake_uow)  # pending
        
        # Make one overdue
        case_overdue = fake_uow.cases.get_by_case_number(case1)
        case_overdue.deadline = datetime.now() - timedelta(days=1)
        fake_uow.cases.update(case_overdue)
        
        # Get statistics
        stats = services.get_case_statistics(uow=fake_uow)
        
        # Verify statistics
        assert stats['status_counts']['pending_acceptance'] == 2
        assert stats['overdue_count'] == 1
        assert stats['total_active_cases'] == 2


class TestServiceLayerIntegration:
    """Integration tests for multiple services working together"""
    
    def test_complete_workflow(self):
        """Test a complete workflow from dentist registration to case completion"""
        fake_uow = uow.FakeUnitOfWork()
        
        # 1. Register dentist
        dentist_id = services.register_dentist(
            first_name="Dr. Complete",
            last_name="Workflow",
            clinic_name="Test Clinic",
            phone_number="555-1234",
            uow=fake_uow
        )
        
        # 2. Register patient
        patient_id = services.register_patient(
            dentist_id=dentist_id,
            first_name="Test",
            last_name="Patient",
            phone_number="555-5678",
            uow=fake_uow
        )
        
        # 3. Create case with items
        items = [{'item_id': 50, 'quantity': 1, 'unit_id': 1}]
        case_number = services.create_new_case(
            dentist_id=dentist_id,
            patient_id=patient_id,
            items=items,
            notes="Complete workflow test",
            uow=fake_uow
        )
        
        # 4. Create task for the case
        task_id = services.create_task(
            case_number=case_number,
            title="Complete workflow task",
            description="Task for complete workflow test",
            estimated_duration=timedelta(hours=2),
            uow=fake_uow
        )
        
        # 5. Start case
        services.start_case(case_number, uow=fake_uow)
        
        # 6. Assign and complete task
        services.assign_task(task_id, assigned_to=500, uow=fake_uow)
        services.start_task(task_id, started_by=500, uow=fake_uow)
        services.complete_task(task_id, completed_by=500, uow=fake_uow)
        
        # 7. Complete case items
        case = fake_uow.cases.get_by_case_number(case_number)
        for item in case.items:
            item.complete_production(timedelta(hours=1))
        case.status = model.CaseStatus.READY_TO_SHIP
        fake_uow.cases.update(case)
        
        # 8. Complete case
        services.complete_case(case_number, completed_by=500, uow=fake_uow)
        
        # Verify final state
        final_case = fake_uow.cases.get_by_case_number(case_number)
        final_task = fake_uow.tasks.get_by_task_id(task_id)
        
        assert final_case.status == model.CaseStatus.COMPLETED
        assert final_task.status == model.TaskStatus.COMPLETED
        assert final_case.actual_completion is not None
        
        # Verify relationships
        assert final_case.dentist_id == dentist_id
        assert final_case.patient_id == patient_id
        assert final_task.case_number == case_number