# DENTAL LAB DDD - Django Repository Implementations
# Concrete implementations that translate between domain models and ORM models

from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from decimal import Decimal

from dental_lab.domain import model
from dental_lab.adapters import repository, orm
from dental_lab.domain.exceptions import CaseNotFound, PatientNotFound, DentistNotFound


class DjangoCaseRepository(repository.AbstractCaseRepository):
    """Django implementation of Case repository"""
    
    def add(self, case: model.Case) -> None:
        """Add a new case to the repository"""
        orm_case = self._to_orm(case)
        orm_case.save()
        
        # Save case items
        for item in case.items:
            self._save_case_item(item)
        
        # Save tasks  
        for task in case.tasks:
            self._save_task(task)
    
    def get(self, case_number: int) -> Optional[model.Case]:
        """Get case by case number"""
        return self.get_by_case_number(case_number)
    
    def get_by_case_number(self, case_number: int) -> Optional[model.Case]:
        """Get a case by its case number"""
        try:
            orm_case = orm.Case.objects.get(case_number=case_number)
            return self._to_domain(orm_case)
        except orm.Case.DoesNotExist:
            return None
    
    def update(self, case: model.Case) -> None:
        """Update an existing case"""
        try:
            orm_case = orm.Case.objects.get(case_number=case.case_number)
            self._update_orm_from_domain(orm_case, case)
            orm_case.save()
            
            # Update case items
            self._update_case_items(case)
            
            # Update tasks
            self._update_tasks(case)
            
        except orm.Case.DoesNotExist:
            raise CaseNotFound(case.case_number)
    
    def delete(self, case_number: int) -> None:
        """Delete a case (soft delete by marking as cancelled)"""
        try:
            orm_case = orm.Case.objects.get(case_number=case_number)
            orm_case.status = 'cancelled'
            orm_case.save()
        except orm.Case.DoesNotExist:
            raise CaseNotFound(case_number)
    
    def list_active_cases(self) -> List[model.Case]:
        """Get all active cases"""
        orm_cases = orm.Case.objects.exclude(
            status__in=['completed', 'cancelled', 'delivered']
        ).order_by('-created_at')
        
        return [self._to_domain(orm_case) for orm_case in orm_cases]
    
    def list_overdue_cases(self) -> List[model.Case]:
        """Get all overdue cases"""
        now = datetime.now()
        orm_cases = orm.Case.objects.filter(
            deadline__lt=now
        ).exclude(
            status__in=['completed', 'cancelled', 'delivered']
        ).order_by('deadline')
        
        return [self._to_domain(orm_case) for orm_case in orm_cases]
    
    def list_cases_by_dentist(self, dentist_id: int) -> List[model.Case]:
        """Get all cases for a specific dentist"""
        orm_cases = orm.Case.objects.filter(
            dentist_id=dentist_id
        ).order_by('-created_at')
        
        return [self._to_domain(orm_case) for orm_case in orm_cases]
    
    def list_cases_by_status(self, status: model.CaseStatus) -> List[model.Case]:
        """Get all cases with specific status"""
        orm_cases = orm.Case.objects.filter(
            status=status.value
        ).order_by('-created_at')
        
        return [self._to_domain(orm_case) for orm_case in orm_cases]
    
    def list_cases_by_department(self, department_id: int) -> List[model.Case]:
        """Get all cases assigned to a specific department"""
        orm_cases = orm.Case.objects.filter(
            responsible_department_id=department_id
        ).order_by('-created_at')
        
        return [self._to_domain(orm_case) for orm_case in orm_cases]
    
    def list_cases_due_soon(self, days: int = 3) -> List[model.Case]:
        """Get cases due within specified days"""
        cutoff_date = datetime.now() + timedelta(days=days)
        orm_cases = orm.Case.objects.filter(
            deadline__lte=cutoff_date,
            deadline__gte=datetime.now()
        ).exclude(
            status__in=['completed', 'cancelled', 'delivered']
        ).order_by('deadline')
        
        return [self._to_domain(orm_case) for orm_case in orm_cases]
    
    def search_cases(self, criteria: Dict[str, Any]) -> List[model.Case]:
        """Search cases by various criteria"""
        queryset = orm.Case.objects.all()
        
        if 'status' in criteria:
            queryset = queryset.filter(status=criteria['status'])
        
        if 'dentist_id' in criteria:
            queryset = queryset.filter(dentist_id=criteria['dentist_id'])
        
        if 'patient_id' in criteria:
            queryset = queryset.filter(patient_id=criteria['patient_id'])
        
        if 'priority' in criteria:
            queryset = queryset.filter(priority=criteria['priority'])
        
        if 'date_from' in criteria:
            queryset = queryset.filter(created_at__gte=criteria['date_from'])
        
        if 'date_to' in criteria:
            queryset = queryset.filter(created_at__lte=criteria['date_to'])
        
        if 'case_number' in criteria:
            queryset = queryset.filter(case_number=criteria['case_number'])
        
        queryset = queryset.order_by('-created_at')
        return [self._to_domain(orm_case) for orm_case in queryset]
    
    def get_next_case_number(self) -> int:
        """Generate the next available case number"""
        last_case = orm.Case.objects.order_by('-case_number').first()
        if last_case:
            return last_case.case_number + 1
        return 1000  # Starting case number
    
    def count_cases_by_status(self) -> Dict[str, int]:
        """Get count of cases grouped by status"""
        from django.db.models import Count
        
        counts = orm.Case.objects.values('status').annotate(
            count=Count('case_number')
        ).order_by('status')
        
        return {item['status']: item['count'] for item in counts}
    
    # Helper methods for domain <-> ORM conversion
    
    def _to_domain(self, orm_case: orm.Case) -> model.Case:
        """Convert ORM Case to domain Case"""
        # Create the case
        case = model.Case(
            case_number=orm_case.case_number,
            dentist_id=orm_case.dentist_id,
            patient_id=orm_case.patient_id,
            status=model.CaseStatus(orm_case.status),
            deadline=orm_case.deadline,
            created_at=orm_case.created_at
        )
        
        # Set additional attributes
        case.priority = model.Priority(orm_case.priority)
        case.received_date_time = orm_case.received_date_time
        case.estimated_completion = orm_case.estimated_completion
        case.actual_completion = orm_case.actual_completion
        case.ship_date_time = orm_case.ship_date_time
        case.delivery_date = orm_case.delivery_date
        case.progress_percentage = orm_case.progress_percentage
        case.revision_count = orm_case.revision_count
        case.workflow_template_id = orm_case.workflow_template_id
        case.current_stage_id = orm_case.current_stage_id
        case.responsible_department_id = orm_case.responsible_department_id
        case.assigned_technician_ids = set(orm_case.assigned_technician_ids)
        case.selected_teeth = set(orm_case.selected_teeth)
        case.quality_checks = orm_case.quality_checks
        case.patient_feedback = orm_case.patient_feedback
        case.dentist_feedback = orm_case.dentist_feedback
        case.notes = orm_case.notes
        case.special_requirements = orm_case.special_requirements
        case.teeth_color = orm_case.teeth_color
        case.delay_reason = orm_case.delay_reason
        
        # Set cost estimate
        if orm_case.cost_estimate_amount:
            case.cost_estimate = model.Money(
                orm_case.cost_estimate_amount,
                orm_case.cost_estimate_currency
            )
        
        # Load case items
        case.items = self._load_case_items(case.case_number)
        
        # Load tasks
        case.tasks = self._load_tasks(case.case_number)
        
        return case
    
    def _to_orm(self, case: model.Case) -> orm.Case:
        """Convert domain Case to ORM Case"""
        orm_case = orm.Case(
            case_number=case.case_number,
            dentist_id=case.dentist_id,
            patient_id=case.patient_id,
            status=case.status.value,
            priority=case.priority.value,
            deadline=case.deadline,
            created_at=case.created_at,
            received_date_time=case.received_date_time,
            estimated_completion=case.estimated_completion,
            actual_completion=case.actual_completion,
            ship_date_time=case.ship_date_time,
            delivery_date=case.delivery_date,
            progress_percentage=case.progress_percentage,
            revision_count=case.revision_count,
            workflow_template_id=case.workflow_template_id,
            current_stage_id=case.current_stage_id,
            responsible_department_id=case.responsible_department_id,
            assigned_technician_ids=list(case.assigned_technician_ids),
            selected_teeth=list(case.selected_teeth),
            quality_checks=case.quality_checks,
            patient_feedback=case.patient_feedback,
            dentist_feedback=case.dentist_feedback,
            notes=case.notes,
            special_requirements=case.special_requirements,
            teeth_color=case.teeth_color,
            delay_reason=case.delay_reason
        )
        
        # Set cost estimate
        if case.cost_estimate:
            orm_case.cost_estimate_amount = case.cost_estimate.amount
            orm_case.cost_estimate_currency = case.cost_estimate.currency
        
        return orm_case
    
    def _update_orm_from_domain(self, orm_case: orm.Case, case: model.Case) -> None:
        """Update ORM case with data from domain case"""
        orm_case.status = case.status.value
        orm_case.priority = case.priority.value
        orm_case.deadline = case.deadline
        orm_case.received_date_time = case.received_date_time
        orm_case.estimated_completion = case.estimated_completion
        orm_case.actual_completion = case.actual_completion
        orm_case.ship_date_time = case.ship_date_time
        orm_case.delivery_date = case.delivery_date
        orm_case.progress_percentage = case.progress_percentage
        orm_case.revision_count = case.revision_count
        orm_case.workflow_template_id = case.workflow_template_id
        orm_case.current_stage_id = case.current_stage_id
        orm_case.responsible_department_id = case.responsible_department_id
        orm_case.assigned_technician_ids = list(case.assigned_technician_ids)
        orm_case.selected_teeth = list(case.selected_teeth)
        orm_case.quality_checks = case.quality_checks
        orm_case.patient_feedback = case.patient_feedback
        orm_case.dentist_feedback = case.dentist_feedback
        orm_case.notes = case.notes
        orm_case.special_requirements = case.special_requirements
        orm_case.teeth_color = case.teeth_color
        orm_case.delay_reason = case.delay_reason
        
        # Update cost estimate
        if case.cost_estimate:
            orm_case.cost_estimate_amount = case.cost_estimate.amount
            orm_case.cost_estimate_currency = case.cost_estimate.currency
        else:
            orm_case.cost_estimate_amount = None
            orm_case.cost_estimate_currency = 'EUR'
    
    def _load_case_items(self, case_number: int) -> List[model.CaseItem]:
        """Load case items for a case"""
        orm_items = orm.CaseItem.objects.filter(case_number=case_number)
        items = []
        
        for orm_item in orm_items:
            item = model.CaseItem(
                case_number=orm_item.case_number,
                item_id=orm_item.item_id,
                quantity=orm_item.quantity,
                unit_id=orm_item.unit_id,
                estimated_time=timedelta(seconds=orm_item.estimated_time_seconds),
                notes=orm_item.notes
            )
            
            item.status = model.TaskStatus(orm_item.status)
            if orm_item.actual_time_seconds:
                item.actual_time = timedelta(seconds=orm_item.actual_time_seconds)
            
            items.append(item)
        
        return items
    
    def _load_tasks(self, case_number: int) -> List[model.Task]:
        """Load tasks for a case"""
        orm_tasks = orm.Task.objects.filter(case_number=case_number)
        tasks = []
        
        for orm_task in orm_tasks:
            task = model.Task(
                task_id=orm_task.task_id,
                case_number=orm_task.case_number,
                title=orm_task.title,
                estimated_duration=timedelta(seconds=orm_task.estimated_duration_seconds),
                assigned_to=orm_task.assigned_to,
                case_item_id=orm_task.case_item_id,
                workflow_stage_id=orm_task.workflow_stage_id
            )
            
            # Set additional attributes
            task.status = model.TaskStatus(orm_task.status)
            task.priority = model.Priority(orm_task.priority)
            task.progress_percentage = orm_task.progress_percentage
            task.actual_start_time = orm_task.actual_start_time
            task.actual_end_time = orm_task.actual_end_time
            task.pause_started = orm_task.pause_started
            task.dependencies = set(orm_task.dependencies)
            task.quality_checklist = orm_task.quality_checklist
            task.quality_check_passed = orm_task.quality_check_passed
            task.blocking_issues = orm_task.blocking_issues
            task.review_comments = orm_task.review_comments
            
            if orm_task.actual_duration_seconds:
                task.actual_duration = timedelta(seconds=orm_task.actual_duration_seconds)
            
            if orm_task.paused_time_seconds:
                task.paused_time = timedelta(seconds=orm_task.paused_time_seconds)
            
            tasks.append(task)
        
        return tasks
    
    def _save_case_item(self, item: model.CaseItem) -> None:
        """Save a case item"""
        orm_item, created = orm.CaseItem.objects.update_or_create(
            case_number=item.case_number,
            item_id=item.item_id,
            defaults={
                'quantity': item.quantity,
                'unit_id': item.unit_id,
                'status': item.status.value,
                'notes': item.notes,
                'estimated_time_seconds': int(item.estimated_time.total_seconds()),
                'actual_time_seconds': int(item.actual_time.total_seconds()) if item.actual_time else None,
            }
        )
    
    def _save_task(self, task: model.Task) -> None:
        """Save a task"""
        orm_task, created = orm.Task.objects.update_or_create(
            task_id=task.task_id,
            defaults={
                'case_number': task.case_number,
                'title': task.title,
                'description': getattr(task, 'description', ''),
                'status': task.status.value,
                'priority': task.priority.value,
                'estimated_duration_seconds': int(task.estimated_duration.total_seconds()),
                'actual_duration_seconds': int(task.actual_duration.total_seconds()) if task.actual_duration else None,
                'paused_time_seconds': int(task.paused_time.total_seconds()) if task.paused_time else 0,
                'progress_percentage': task.progress_percentage,
                'assigned_to': task.assigned_to,
                'case_item_id': task.case_item_id,
                'workflow_stage_id': task.workflow_stage_id,
                'actual_start_time': task.actual_start_time,
                'actual_end_time': task.actual_end_time,
                'pause_started': task.pause_started,
                'dependencies': list(task.dependencies),
                'quality_checklist': task.quality_checklist,
                'quality_check_passed': task.quality_check_passed,
                'blocking_issues': task.blocking_issues,
                'review_comments': task.review_comments,
            }
        )
    
    def _update_case_items(self, case: model.Case) -> None:
        """Update case items for a case"""
        # Delete existing items not in the domain case
        existing_item_ids = set(
            orm.CaseItem.objects.filter(case_number=case.case_number)
            .values_list('item_id', flat=True)
        )
        domain_item_ids = {item.item_id for item in case.items}
        items_to_delete = existing_item_ids - domain_item_ids
        
        orm.CaseItem.objects.filter(
            case_number=case.case_number,
            item_id__in=items_to_delete
        ).delete()
        
        # Save/update current items
        for item in case.items:
            self._save_case_item(item)
    
    def _update_tasks(self, case: model.Case) -> None:
        """Update tasks for a case"""
        # Delete existing tasks not in the domain case
        existing_task_ids = set(
            orm.Task.objects.filter(case_number=case.case_number)
            .values_list('task_id', flat=True)
        )
        domain_task_ids = {task.task_id for task in case.tasks}
        tasks_to_delete = existing_task_ids - domain_task_ids
        
        orm.Task.objects.filter(task_id__in=tasks_to_delete).delete()
        
        # Save/update current tasks
        for task in case.tasks:
            self._save_task(task)


class DjangoPatientRepository(repository.AbstractPatientRepository):
    """Django implementation of Patient repository"""
    
    def add(self, patient: model.Patient) -> None:
        """Add a new patient"""
        orm_patient = self._to_orm(patient)
        orm_patient.save()
    
    def get(self, patient_id: int) -> Optional[model.Patient]:
        """Get patient by ID"""
        return self.get_by_patient_id(patient_id)
    
    def get_by_patient_id(self, patient_id: int) -> Optional[model.Patient]:
        """Get a patient by patient ID"""
        try:
            orm_patient = orm.Patient.objects.get(patient_id=patient_id)
            return self._to_domain(orm_patient)
        except orm.Patient.DoesNotExist:
            return None
    
    def update(self, patient: model.Patient) -> None:
        """Update an existing patient"""
        try:
            orm_patient = orm.Patient.objects.get(patient_id=patient.patient_id)
            self._update_orm_from_domain(orm_patient, patient)
            orm_patient.save()
        except orm.Patient.DoesNotExist:
            raise PatientNotFound(patient.patient_id)
    
    def delete(self, patient_id: int) -> None:
        """Delete a patient"""
        try:
            orm.Patient.objects.get(patient_id=patient_id).delete()
        except orm.Patient.DoesNotExist:
            raise PatientNotFound(patient_id)
    
    def list_patients_by_dentist(self, dentist_id: int) -> List[model.Patient]:
        """Get all patients for a specific dentist"""
        orm_patients = orm.Patient.objects.filter(
            dentist_id=dentist_id
        ).order_by('first_name', 'last_name')
        
        return [self._to_domain(orm_patient) for orm_patient in orm_patients]
    
    def search_patients(self, name_query: str, dentist_id: Optional[int] = None) -> List[model.Patient]:
        """Search patients by name"""
        from django.db.models import Q
        
        queryset = orm.Patient.objects.filter(
            Q(first_name__icontains=name_query) | Q(last_name__icontains=name_query)
        )
        
        if dentist_id is not None:
            queryset = queryset.filter(dentist_id=dentist_id)
        
        queryset = queryset.order_by('first_name', 'last_name')
        return [self._to_domain(orm_patient) for orm_patient in queryset]
    
    def get_patient_with_cases(self, patient_id: int) -> Optional[model.Patient]:
        """Get patient with their associated cases loaded"""
        # For now, just return the patient - cases would be loaded separately
        # In a more complex implementation, this could eagerly load related data
        return self.get_by_patient_id(patient_id)
    
    def list_patients_with_active_cases(self) -> List[model.Patient]:
        """Get patients who currently have active cases"""
        active_case_patient_ids = orm.Case.objects.exclude(
            status__in=['completed', 'cancelled', 'delivered']
        ).values_list('patient_id', flat=True).distinct()
        
        orm_patients = orm.Patient.objects.filter(
            patient_id__in=active_case_patient_ids
        ).order_by('first_name', 'last_name')
        
        return [self._to_domain(orm_patient) for orm_patient in orm_patients]
    
    def _to_domain(self, orm_patient: orm.Patient) -> model.Patient:
        """Convert ORM Patient to domain Patient"""
        name = model.PersonName(orm_patient.first_name, orm_patient.last_name)
        contact = model.ContactInfo(
            phone=orm_patient.phone_number or None,
            email=orm_patient.email or None,
            address=orm_patient.address or None
        )
        
        return model.Patient(
            patient_id=orm_patient.patient_id,
            name=name,
            dentist_id=orm_patient.dentist_id,
            contact=contact,
            date_of_birth=orm_patient.date_of_birth
        )
    
    def _to_orm(self, patient: model.Patient) -> orm.Patient:
        """Convert domain Patient to ORM Patient"""
        return orm.Patient(
            patient_id=patient.patient_id,
            first_name=patient.name.first_name,
            last_name=patient.name.last_name,
            dentist_id=patient.dentist_id,
            phone_number=patient.contact.phone or '',
            email=patient.contact.email or '',
            address=patient.contact.address or '',
            date_of_birth=patient.date_of_birth
        )
    
    def _update_orm_from_domain(self, orm_patient: orm.Patient, patient: model.Patient) -> None:
        """Update ORM patient with data from domain patient"""
        orm_patient.first_name = patient.name.first_name
        orm_patient.last_name = patient.name.last_name
        orm_patient.dentist_id = patient.dentist_id
        orm_patient.phone_number = patient.contact.phone or ''
        orm_patient.email = patient.contact.email or ''
        orm_patient.address = patient.contact.address or ''
        orm_patient.date_of_birth = patient.date_of_birth


class DjangoDentistRepository(repository.AbstractDentistRepository):
    """Django implementation of Dentist repository"""
    
    def add(self, dentist: model.Dentist) -> None:
        """Add a new dentist"""
        orm_dentist = self._to_orm(dentist)
        orm_dentist.save()
    
    def get(self, dentist_id: int) -> Optional[model.Dentist]:
        """Get dentist by ID"""
        return self.get_by_dentist_id(dentist_id)
    
    def get_by_dentist_id(self, dentist_id: int) -> Optional[model.Dentist]:
        """Get a dentist by dentist ID"""
        try:
            orm_dentist = orm.Dentist.objects.get(dentist_id=dentist_id)
            return self._to_domain(orm_dentist)
        except orm.Dentist.DoesNotExist:
            return None
    
    def get_by_user_id(self, user_id: int) -> Optional[model.Dentist]:
        """Get a dentist by their user account ID"""
        try:
            orm_dentist = orm.Dentist.objects.get(user_id=user_id)
            return self._to_domain(orm_dentist)
        except orm.Dentist.DoesNotExist:
            return None
    
    def update(self, dentist: model.Dentist) -> None:
        """Update an existing dentist"""
        try:
            orm_dentist = orm.Dentist.objects.get(dentist_id=dentist.dentist_id)
            self._update_orm_from_domain(orm_dentist, dentist)
            orm_dentist.save()
        except orm.Dentist.DoesNotExist:
            raise DentistNotFound(dentist.dentist_id)
    
    def delete(self, dentist_id: int) -> None:
        """Delete a dentist (soft delete by marking inactive)"""
        try:
            orm_dentist = orm.Dentist.objects.get(dentist_id=dentist_id)
            orm_dentist.is_active = False
            orm_dentist.save()
        except orm.Dentist.DoesNotExist:
            raise DentistNotFound(dentist_id)
    
    def list_all_dentists(self) -> List[model.Dentist]:
        """Get all registered dentists"""
        orm_dentists = orm.Dentist.objects.filter(
            is_active=True
        ).order_by('first_name', 'last_name')
        
        return [self._to_domain(orm_dentist) for orm_dentist in orm_dentists]
    
    def search_dentists(self, name_query: str) -> List[model.Dentist]:
        """Search dentists by name or clinic"""
        from django.db.models import Q
        
        orm_dentists = orm.Dentist.objects.filter(
            Q(first_name__icontains=name_query) | 
            Q(last_name__icontains=name_query) |
            Q(clinic_name__icontains=name_query),
            is_active=True
        ).order_by('first_name', 'last_name')
        
        return [self._to_domain(orm_dentist) for orm_dentist in orm_dentists]
    
    def get_dentist_with_patients(self, dentist_id: int) -> Optional[model.Dentist]:
        """Get dentist with their patients loaded"""
        # For now, just return the dentist - patients would be loaded separately
        return self.get_by_dentist_id(dentist_id)
    
    def list_dentists_with_active_cases(self) -> List[model.Dentist]:
        """Get dentists who currently have active cases"""
        active_case_dentist_ids = orm.Case.objects.exclude(
            status__in=['completed', 'cancelled', 'delivered']
        ).values_list('dentist_id', flat=True).distinct()
        
        orm_dentists = orm.Dentist.objects.filter(
            dentist_id__in=active_case_dentist_ids,
            is_active=True
        ).order_by('first_name', 'last_name')
        
        return [self._to_domain(orm_dentist) for orm_dentist in orm_dentists]
    
    def _to_domain(self, orm_dentist: orm.Dentist) -> model.Dentist:
        """Convert ORM Dentist to domain Dentist"""
        name = model.PersonName(orm_dentist.first_name, orm_dentist.last_name)
        contact = model.ContactInfo(
            phone=orm_dentist.phone_number or None,
            email=orm_dentist.email or None,
            address=orm_dentist.address or None
        )
        
        return model.Dentist(
            dentist_id=orm_dentist.dentist_id,
            name=name,
            clinic_name=orm_dentist.clinic_name,
            contact=contact,
            user_id=orm_dentist.user_id
        )
    
    def _to_orm(self, dentist: model.Dentist) -> orm.Dentist:
        """Convert domain Dentist to ORM Dentist"""
        return orm.Dentist(
            dentist_id=dentist.dentist_id,
            first_name=dentist.name.first_name,
            last_name=dentist.name.last_name,
            clinic_name=dentist.clinic_name,
            phone_number=dentist.contact.phone or '',
            email=dentist.contact.email or '',
            address=dentist.contact.address or '',
            user_id=dentist.user_id,
            is_active=True
        )
    
    def _update_orm_from_domain(self, orm_dentist: orm.Dentist, dentist: model.Dentist) -> None:
        """Update ORM dentist with data from domain dentist"""
        orm_dentist.first_name = dentist.name.first_name
        orm_dentist.last_name = dentist.name.last_name
        orm_dentist.clinic_name = dentist.clinic_name
        orm_dentist.phone_number = dentist.contact.phone or ''
        orm_dentist.email = dentist.contact.email or ''
        orm_dentist.address = dentist.contact.address or ''
        orm_dentist.user_id = dentist.user_id


class DjangoTaskRepository(repository.AbstractTaskRepository):
    """Django implementation of Task repository"""
    
    def add(self, task: model.Task) -> None:
        """Add a new task"""
        orm_task = self._to_orm(task)
        orm_task.save()
    
    def get(self, task_id: int) -> Optional[model.Task]:
        """Get task by ID"""
        return self.get_by_task_id(task_id)
    
    def get_by_task_id(self, task_id: int) -> Optional[model.Task]:
        """Get a task by task ID"""
        try:
            orm_task = orm.Task.objects.get(task_id=task_id)
            return self._to_domain(orm_task)
        except orm.Task.DoesNotExist:
            return None
    
    def update(self, task: model.Task) -> None:
        """Update an existing task"""
        try:
            orm_task = orm.Task.objects.get(task_id=task.task_id)
            self._update_orm_from_domain(orm_task, task)
            orm_task.save()
        except orm.Task.DoesNotExist:
            from dental_lab.domain.exceptions import TaskNotFound
            raise TaskNotFound(task.task_id)
    
    def delete(self, task_id: int) -> None:
        """Delete a task"""
        try:
            orm.Task.objects.get(task_id=task_id).delete()
        except orm.Task.DoesNotExist:
            from dental_lab.domain.exceptions import TaskNotFound
            raise TaskNotFound(task_id)
    
    def list_tasks_by_case(self, case_number: int) -> List[model.Task]:
        """Get all tasks for a specific case"""
        orm_tasks = orm.Task.objects.filter(
            case_number=case_number
        ).order_by('created_at')
        
        return [self._to_domain(orm_task) for orm_task in orm_tasks]
    
    def list_tasks_by_assignee(self, assignee_id: int) -> List[model.Task]:
        """Get all tasks assigned to a specific person"""
        orm_tasks = orm.Task.objects.filter(
            assigned_to=assignee_id
        ).order_by('-priority', 'created_at')
        
        return [self._to_domain(orm_task) for orm_task in orm_tasks]
    
    def list_tasks_by_status(self, status: model.TaskStatus) -> List[model.Task]:
        """Get all tasks with specific status"""
        orm_tasks = orm.Task.objects.filter(
            status=status.value
        ).order_by('-priority', 'created_at')
        
        return [self._to_domain(orm_task) for orm_task in orm_tasks]
    
    def list_overdue_tasks(self) -> List[model.Task]:
        """Get all overdue tasks"""
        # For simplicity, we'll consider tasks overdue if they've been in progress
        # longer than their estimated duration
        now = datetime.now()
        orm_tasks = orm.Task.objects.filter(
            status='in_progress',
            actual_start_time__isnull=False
        ).order_by('actual_start_time')
        
        overdue_tasks = []
        for orm_task in orm_tasks:
            estimated_end = orm_task.actual_start_time + timedelta(
                seconds=orm_task.estimated_duration_seconds
            )
            if now > estimated_end:
                overdue_tasks.append(self._to_domain(orm_task))
        
        return overdue_tasks
    
    def list_pending_tasks(self, assignee_id: Optional[int] = None) -> List[model.Task]:
        """Get pending tasks, optionally filtered by assignee"""
        queryset = orm.Task.objects.filter(status='pending')
        
        if assignee_id is not None:
            queryset = queryset.filter(assigned_to=assignee_id)
        
        orm_tasks = queryset.order_by('-priority', 'created_at')
        return [self._to_domain(orm_task) for orm_task in orm_tasks]
    
    def list_blocked_tasks(self) -> List[model.Task]:
        """Get all blocked tasks"""
        orm_tasks = orm.Task.objects.filter(
            status='blocked'
        ).order_by('-priority', 'created_at')
        
        return [self._to_domain(orm_task) for orm_task in orm_tasks]
    
    def list_tasks_by_workflow_stage(self, stage_id: int) -> List[model.Task]:
        """Get tasks by workflow stage"""
        orm_tasks = orm.Task.objects.filter(
            workflow_stage_id=stage_id
        ).order_by('-priority', 'created_at')
        
        return [self._to_domain(orm_task) for orm_task in orm_tasks]
    
    def get_next_task_id(self) -> int:
        """Generate the next available task ID"""
        last_task = orm.Task.objects.order_by('-task_id').first()
        if last_task:
            return last_task.task_id + 1
        return 1
    
    def _to_domain(self, orm_task: orm.Task) -> model.Task:
        """Convert ORM Task to domain Task"""
        task = model.Task(
            task_id=orm_task.task_id,
            case_number=orm_task.case_number,
            title=orm_task.title,
            estimated_duration=timedelta(seconds=orm_task.estimated_duration_seconds),
            assigned_to=orm_task.assigned_to,
            case_item_id=orm_task.case_item_id,
            workflow_stage_id=orm_task.workflow_stage_id
        )
        
        # Set additional attributes
        task.status = model.TaskStatus(orm_task.status)
        task.priority = model.Priority(orm_task.priority)
        task.progress_percentage = orm_task.progress_percentage
        task.actual_start_time = orm_task.actual_start_time
        task.actual_end_time = orm_task.actual_end_time
        task.pause_started = orm_task.pause_started
        task.dependencies = set(orm_task.dependencies)
        task.quality_checklist = orm_task.quality_checklist
        task.quality_check_passed = orm_task.quality_check_passed
        task.blocking_issues = orm_task.blocking_issues
        task.review_comments = orm_task.review_comments
        
        if orm_task.actual_duration_seconds:
            task.actual_duration = timedelta(seconds=orm_task.actual_duration_seconds)
        
        if orm_task.paused_time_seconds:
            task.paused_time = timedelta(seconds=orm_task.paused_time_seconds)
        
        return task
    
    def _to_orm(self, task: model.Task) -> orm.Task:
        """Convert domain Task to ORM Task"""
        return orm.Task(
            task_id=task.task_id,
            case_number=task.case_number,
            title=task.title,
            description=getattr(task, 'description', ''),
            status=task.status.value,
            priority=task.priority.value,
            estimated_duration_seconds=int(task.estimated_duration.total_seconds()),
            actual_duration_seconds=int(task.actual_duration.total_seconds()) if task.actual_duration else None,
            paused_time_seconds=int(task.paused_time.total_seconds()) if task.paused_time else 0,
            progress_percentage=task.progress_percentage,
            assigned_to=task.assigned_to,
            case_item_id=task.case_item_id,
            workflow_stage_id=task.workflow_stage_id,
            actual_start_time=task.actual_start_time,
            actual_end_time=task.actual_end_time,
            pause_started=task.pause_started,
            dependencies=list(task.dependencies),
            quality_checklist=task.quality_checklist,
            quality_check_passed=task.quality_check_passed,
            blocking_issues=task.blocking_issues,
            review_comments=task.review_comments,
        )
    
    def _update_orm_from_domain(self, orm_task: orm.Task, task: model.Task) -> None:
        """Update ORM task with data from domain task"""
        orm_task.title = task.title
        orm_task.description = getattr(task, 'description', '')
        orm_task.status = task.status.value
        orm_task.priority = task.priority.value
        orm_task.estimated_duration_seconds = int(task.estimated_duration.total_seconds())
        orm_task.actual_duration_seconds = int(task.actual_duration.total_seconds()) if task.actual_duration else None
        orm_task.paused_time_seconds = int(task.paused_time.total_seconds()) if task.paused_time else 0
        orm_task.progress_percentage = task.progress_percentage
        orm_task.assigned_to = task.assigned_to
        orm_task.case_item_id = task.case_item_id
        orm_task.workflow_stage_id = task.workflow_stage_id
        orm_task.actual_start_time = task.actual_start_time
        orm_task.actual_end_time = task.actual_end_time
        orm_task.pause_started = task.pause_started
        orm_task.dependencies = list(task.dependencies)
        orm_task.quality_checklist = task.quality_checklist
        orm_task.quality_check_passed = task.quality_check_passed
        orm_task.blocking_issues = task.blocking_issues
        orm_task.review_comments = task.review_comments


class DjangoCaseItemRepository(repository.AbstractCaseItemRepository):
    """Django implementation of CaseItem repository"""
    
    def add(self, case_item: model.CaseItem) -> None:
        """Add a new case item"""
        orm_case_item = self._to_orm(case_item)
        orm_case_item.save()
    
    def get(self, case_item_id: int) -> Optional[model.CaseItem]:
        """Get case item by ID"""
        try:
            orm_case_item = orm.CaseItem.objects.get(id=case_item_id)
            return self._to_domain(orm_case_item)
        except orm.CaseItem.DoesNotExist:
            return None
    
    def update(self, case_item: model.CaseItem) -> None:
        """Update an existing case item"""
        try:
            orm_case_item = orm.CaseItem.objects.get(
                case_number=case_item.case_number,
                item_id=case_item.item_id
            )
            self._update_orm_from_domain(orm_case_item, case_item)
            orm_case_item.save()
        except orm.CaseItem.DoesNotExist:
            from dental_lab.domain.exceptions import ItemNotFound
            raise ItemNotFound(case_item.item_id)
    
    def delete(self, case_item_id: int) -> None:
        """Delete a case item"""
        try:
            orm.CaseItem.objects.get(id=case_item_id).delete()
        except orm.CaseItem.DoesNotExist:
            from dental_lab.domain.exceptions import ItemNotFound
            raise ItemNotFound(case_item_id)
    
    def list_items_by_case(self, case_number: int) -> List[model.CaseItem]:
        """Get all items for a specific case"""
        orm_items = orm.CaseItem.objects.filter(case_number=case_number)
        return [self._to_domain(orm_item) for orm_item in orm_items]
    
    def get_case_item(self, case_number: int, item_id: int) -> Optional[model.CaseItem]:
        """Get a specific case item"""
        try:
            orm_case_item = orm.CaseItem.objects.get(
                case_number=case_number,
                item_id=item_id
            )
            return self._to_domain(orm_case_item)
        except orm.CaseItem.DoesNotExist:
            return None
    
    def list_items_by_status(self, status: model.TaskStatus) -> List[model.CaseItem]:
        """Get case items by production status"""
        orm_items = orm.CaseItem.objects.filter(status=status.value)
        return [self._to_domain(orm_item) for orm_item in orm_items]
    
    def list_items_in_production(self) -> List[model.CaseItem]:
        """Get all items currently in production"""
        orm_items = orm.CaseItem.objects.filter(status='in_progress')
        return [self._to_domain(orm_item) for orm_item in orm_items]
    
    def list_delayed_items(self) -> List[model.CaseItem]:
        """Get items that are delayed in production"""
        # Items are delayed if actual time > estimated time
        orm_items = orm.CaseItem.objects.filter(
            actual_time_seconds__gt=models.F('estimated_time_seconds'),
            status__in=['completed']
        )
        return [self._to_domain(orm_item) for orm_item in orm_items]
    
    def _to_domain(self, orm_item: orm.CaseItem) -> model.CaseItem:
        """Convert ORM CaseItem to domain CaseItem"""
        item = model.CaseItem(
            case_number=orm_item.case_number,
            item_id=orm_item.item_id,
            quantity=orm_item.quantity,
            unit_id=orm_item.unit_id,
            estimated_time=timedelta(seconds=orm_item.estimated_time_seconds),
            notes=orm_item.notes
        )
        
        item.status = model.TaskStatus(orm_item.status)
        if orm_item.actual_time_seconds:
            item.actual_time = timedelta(seconds=orm_item.actual_time_seconds)
        
        return item
    
    def _to_orm(self, item: model.CaseItem) -> orm.CaseItem:
        """Convert domain CaseItem to ORM CaseItem"""
        return orm.CaseItem(
            case_number=item.case_number,
            item_id=item.item_id,
            quantity=item.quantity,
            unit_id=item.unit_id,
            status=item.status.value,
            notes=item.notes,
            estimated_time_seconds=int(item.estimated_time.total_seconds()),
            actual_time_seconds=int(item.actual_time.total_seconds()) if item.actual_time else None,
        )
    
    def _update_orm_from_domain(self, orm_item: orm.CaseItem, item: model.CaseItem) -> None:
        """Update ORM case item with data from domain case item"""
        orm_item.quantity = item.quantity
        orm_item.unit_id = item.unit_id
        orm_item.status = item.status.value
        orm_item.notes = item.notes
        orm_item.estimated_time_seconds = int(item.estimated_time.total_seconds())
        orm_item.actual_time_seconds = int(item.actual_time.total_seconds()) if item.actual_time else None


# Repository Factory Implementation
class DjangoRepositoryFactory(repository.AbstractRepositoryFactory):
    """Factory for creating Django repository instances"""
    
    def create_case_repository(self) -> repository.AbstractCaseRepository:
        """Create a case repository instance"""
        return DjangoCaseRepository()
    
    def create_patient_repository(self) -> repository.AbstractPatientRepository:
        """Create a patient repository instance"""
        return DjangoPatientRepository()
    
    def create_dentist_repository(self) -> repository.AbstractDentistRepository:
        """Create a dentist repository instance"""
        return DjangoDentistRepository()
    
    def create_task_repository(self) -> repository.AbstractTaskRepository:
        """Create a task repository instance"""
        return DjangoTaskRepository()
    
    def create_case_item_repository(self) -> repository.AbstractCaseItemRepository:
        """Create a case item repository instance"""
        return DjangoCaseItemRepository()
    
    def create_analytics_repository(self) -> repository.AbstractCaseAnalyticsRepository:
        """Create an analytics repository instance"""
        # Implementation would go here
        raise NotImplementedError("Analytics repository not yet implemented")
    
    def create_event_store(self) -> repository.AbstractEventStore:
        """Create an event store instance"""
        # Implementation would go here
        raise NotImplementedError("Event store not yet implemented")