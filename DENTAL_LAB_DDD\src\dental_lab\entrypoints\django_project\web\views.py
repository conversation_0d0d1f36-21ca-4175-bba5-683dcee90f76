# DENTAL LAB DDD - Thin Django Views
# Views that coordinate between HTTP and service layer

from django.shortcuts import render, redirect, get_object_or_404
from django.contrib import messages
from django.http import JsonResponse, HttpResponse
from django.views.decorators.http import require_http_methods, require_POST
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.views.generic import View
from django.core.paginator import Paginator
from django.contrib.auth.decorators import login_required
from datetime import datetime, timedelta
import json
import logging

from dental_lab.service_layer import services, uow
from dental_lab.domain import exceptions
from . import forms

logger = logging.getLogger(__name__)


# Helper function to get Unit of Work
def get_uow():
    """Get Unit of Work instance"""
    return uow.DjangoUnitOfWork()


# Case Management Views
@login_required
def case_list_view(request):
    """
    View: List all cases with filtering options
    """
    try:
        # Get filter parameters
        status_filter = request.GET.get('status', '')
        dentist_filter = request.GET.get('dentist_id', '')
        search_query = request.GET.get('search', '')
        
        # Build search criteria
        criteria = {}
        if status_filter:
            criteria['status'] = status_filter
        if dentist_filter:
            criteria['dentist_id'] = int(dentist_filter)
        
        # Get cases from service layer
        with get_uow() as unit_of_work:
            if criteria:
                cases = services.search_cases(criteria, unit_of_work)
            else:
                cases = unit_of_work.cases.list_active_cases()
        
        # Pagination
        paginator = Paginator(cases, 20)
        page_number = request.GET.get('page')
        page_obj = paginator.get_page(page_number)
        
        context = {
            'page_obj': page_obj,
            'status_filter': status_filter,
            'dentist_filter': dentist_filter,
            'search_query': search_query,
        }
        
        return render(request, 'cases/case_list.html', context)
        
    except Exception as e:
        logger.error(f"Error in case_list_view: {str(e)}")
        messages.error(request, "Error loading cases. Please try again.")
        return render(request, 'cases/case_list.html', {'page_obj': None})


@login_required
def case_detail_view(request, case_number):
    """
    View: Show detailed information about a case
    """
    try:
        # Get case details from service layer
        case = services.get_case_details(case_number, get_uow())
        
        context = {
            'case': case,
            'can_edit': _can_user_edit_case(request.user, case),
            'can_start': _can_user_start_case(request.user, case),
            'can_complete': _can_user_complete_case(request.user, case),
        }
        
        return render(request, 'cases/case_detail.html', context)
        
    except exceptions.CaseNotFound:
        messages.error(request, f"Case #{case_number} not found.")
        return redirect('case_list')
    except Exception as e:
        logger.error(f"Error in case_detail_view: {str(e)}")
        messages.error(request, "Error loading case details.")
        return redirect('case_list')


@login_required
def create_case_view(request):
    """
    View: Create a new case
    """
    if request.method == 'POST':
        form = forms.CreateCaseForm(request.POST)
        
        if form.is_valid():
            try:
                # Extract form data
                data = form.cleaned_data
                
                # Prepare items if any
                items = []
                if data.get('items'):
                    # Parse items from form data
                    # This would be more complex in a real implementation
                    pass
                
                # Create case using service layer
                case_number = services.create_new_case(
                    dentist_id=data['dentist_id'],
                    patient_id=data.get('patient_id'),
                    deadline=data.get('deadline'),
                    priority=data.get('priority', 2),
                    notes=data.get('notes', ''),
                    special_requirements=data.get('special_requirements', ''),
                    items=items,
                    uow=get_uow()
                )
                
                messages.success(request, f"Case #{case_number} created successfully!")
                return redirect('case_detail', case_number=case_number)
                
            except exceptions.DentistNotFound:
                form.add_error('dentist_id', 'Selected dentist not found.')
            except exceptions.PatientNotFound:
                form.add_error('patient_id', 'Selected patient not found.')
            except exceptions.PatientDentistMismatch:
                form.add_error('patient_id', 'Patient does not belong to selected dentist.')
            except Exception as e:
                logger.error(f"Error creating case: {str(e)}")
                messages.error(request, "Error creating case. Please try again.")
    else:
        form = forms.CreateCaseForm()
    
    context = {
        'form': form,
        'dentists': _get_dentists_for_user(request.user),
    }
    
    return render(request, 'cases/create_case.html', context)


@login_required
@require_POST
def start_case_view(request, case_number):
    """
    View: Start a case (AJAX endpoint)
    """
    try:
        services.start_case(
            case_number=case_number,
            started_by=request.user.id,
            uow=get_uow()
        )
        
        messages.success(request, f"Case #{case_number} started successfully!")
        
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({'success': True, 'message': 'Case started successfully'})
        else:
            return redirect('case_detail', case_number=case_number)
            
    except exceptions.CaseNotFound:
        error_msg = f"Case #{case_number} not found."
    except exceptions.CaseHasNoItems:
        error_msg = "Cannot start case without items."
    except exceptions.InvalidCaseStatus as e:
        error_msg = str(e)
    except Exception as e:
        logger.error(f"Error starting case {case_number}: {str(e)}")
        error_msg = "Error starting case. Please try again."
    
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        return JsonResponse({'success': False, 'error': error_msg})
    else:
        messages.error(request, error_msg)
        return redirect('case_detail', case_number=case_number)


@login_required
@require_POST
def complete_case_view(request, case_number):
    """
    View: Complete a case
    """
    try:
        completion_notes = request.POST.get('completion_notes', '')
        
        services.complete_case(
            case_number=case_number,
            completed_by=request.user.id,
            completion_notes=completion_notes,
            uow=get_uow()
        )
        
        messages.success(request, f"Case #{case_number} completed successfully!")
        return redirect('case_detail', case_number=case_number)
        
    except exceptions.CaseNotFound:
        messages.error(request, f"Case #{case_number} not found.")
    except exceptions.InvalidCaseStatus as e:
        messages.error(request, str(e))
    except exceptions.CaseItemsNotCompleted:
        messages.error(request, "All case items must be completed before completing the case.")
    except Exception as e:
        logger.error(f"Error completing case {case_number}: {str(e)}")
        messages.error(request, "Error completing case. Please try again.")
    
    return redirect('case_detail', case_number=case_number)


@login_required
def add_item_to_case_view(request, case_number):
    """
    View: Add an item to an existing case
    """
    if request.method == 'POST':
        form = forms.AddItemToCaseForm(request.POST)
        
        if form.is_valid():
            try:
                data = form.cleaned_data
                
                services.add_item_to_case(
                    case_number=case_number,
                    item_id=data['item_id'],
                    quantity=data['quantity'],
                    unit_id=data['unit_id'],
                    estimated_time=data.get('estimated_time'),
                    special_instructions=data.get('special_instructions', ''),
                    added_by=request.user.id,
                    uow=get_uow()
                )
                
                messages.success(request, "Item added to case successfully!")
                return redirect('case_detail', case_number=case_number)
                
            except exceptions.CaseNotFound:
                messages.error(request, f"Case #{case_number} not found.")
                return redirect('case_list')
            except exceptions.InvalidQuantity as e:
                form.add_error('quantity', str(e))
            except exceptions.InvalidCaseStatus as e:
                messages.error(request, str(e))
                return redirect('case_detail', case_number=case_number)
            except Exception as e:
                logger.error(f"Error adding item to case {case_number}: {str(e)}")
                messages.error(request, "Error adding item. Please try again.")
    else:
        form = forms.AddItemToCaseForm()
    
    # Get case details for context
    try:
        case = services.get_case_details(case_number, get_uow())
    except exceptions.CaseNotFound:
        messages.error(request, f"Case #{case_number} not found.")
        return redirect('case_list')
    
    context = {
        'form': form,
        'case': case,
        'available_items': _get_available_items(),
    }
    
    return render(request, 'cases/add_item.html', context)


# Dashboard and Analytics Views
@login_required
def dashboard_view(request):
    """
    View: Main dashboard with key metrics and recent activity
    """
    try:
        # Get statistics from service layer
        statistics = services.get_case_statistics(uow=get_uow())
        
        # Get recent cases
        with get_uow() as unit_of_work:
            recent_cases = unit_of_work.cases.list_active_cases()[:10]
            overdue_cases = services.list_overdue_cases(unit_of_work)
            due_soon_cases = services.get_cases_due_soon(days=3, uow=unit_of_work)
        
        context = {
            'statistics': statistics,
            'recent_cases': recent_cases,
            'overdue_cases': overdue_cases[:5],  # Show only first 5
            'due_soon_cases': due_soon_cases[:5],
            'user_role': _get_user_role(request.user),
        }
        
        return render(request, 'dashboard/dashboard.html', context)
        
    except Exception as e:
        logger.error(f"Error in dashboard_view: {str(e)}")
        messages.error(request, "Error loading dashboard. Please try again.")
        return render(request, 'dashboard/dashboard.html', {})


@login_required
def overdue_cases_view(request):
    """
    View: List all overdue cases
    """
    try:
        overdue_cases = services.list_overdue_cases(get_uow())
        
        # Pagination
        paginator = Paginator(overdue_cases, 20)
        page_number = request.GET.get('page')
        page_obj = paginator.get_page(page_number)
        
        context = {
            'page_obj': page_obj,
            'title': 'Overdue Cases',
        }
        
        return render(request, 'cases/overdue_cases.html', context)
        
    except Exception as e:
        logger.error(f"Error in overdue_cases_view: {str(e)}")
        messages.error(request, "Error loading overdue cases.")
        return render(request, 'cases/overdue_cases.html', {'page_obj': None})


# Patient Management Views
@login_required
def patient_list_view(request):
    """
    View: List patients with search functionality
    """
    search_query = request.GET.get('search', '')
    dentist_filter = request.GET.get('dentist_id', '')
    
    try:
        if search_query:
            dentist_id = int(dentist_filter) if dentist_filter else None
            patients = services.search_patients(
                name_query=search_query,
                dentist_id=dentist_id,
                uow=get_uow()
            )
        else:
            # Get all patients for the user's accessible dentists
            with get_uow() as unit_of_work:
                if dentist_filter:
                    patients = unit_of_work.patients.list_patients_by_dentist(int(dentist_filter))
                else:
                    # This would need to be filtered based on user permissions
                    patients = []
        
        # Pagination
        paginator = Paginator(patients, 20)
        page_number = request.GET.get('page')
        page_obj = paginator.get_page(page_number)
        
        context = {
            'page_obj': page_obj,
            'search_query': search_query,
            'dentist_filter': dentist_filter,
            'dentists': _get_dentists_for_user(request.user),
        }
        
        return render(request, 'patients/patient_list.html', context)
        
    except Exception as e:
        logger.error(f"Error in patient_list_view: {str(e)}")
        messages.error(request, "Error loading patients.")
        return render(request, 'patients/patient_list.html', {'page_obj': None})


@login_required
def create_patient_view(request):
    """
    View: Create a new patient
    """
    if request.method == 'POST':
        form = forms.CreatePatientForm(request.POST)
        
        if form.is_valid():
            try:
                data = form.cleaned_data
                
                patient_id = services.register_patient(
                    dentist_id=data['dentist_id'],
                    first_name=data['first_name'],
                    last_name=data['last_name'],
                    phone_number=data.get('phone_number'),
                    email=data.get('email'),
                    address=data.get('address'),
                    date_of_birth=data.get('date_of_birth'),
                    registered_by=request.user.id,
                    uow=get_uow()
                )
                
                messages.success(request, f"Patient registered successfully (ID: {patient_id})!")
                return redirect('patient_list')
                
            except exceptions.DentistNotFound:
                form.add_error('dentist_id', 'Selected dentist not found.')
            except exceptions.InvalidPatientData as e:
                form.add_error(e.details.get('field', '__all__'), str(e))
            except Exception as e:
                logger.error(f"Error creating patient: {str(e)}")
                messages.error(request, "Error creating patient. Please try again.")
    else:
        form = forms.CreatePatientForm()
    
    context = {
        'form': form,
        'dentists': _get_dentists_for_user(request.user),
    }
    
    return render(request, 'patients/create_patient.html', context)


# API Views for AJAX requests
@login_required
@require_http_methods(["GET"])
def api_case_status(request, case_number):
    """
    API: Get case status and progress information
    """
    try:
        case = services.get_case_details(case_number, get_uow())
        
        return JsonResponse({
            'case_number': case.case_number,
            'status': case.status.value,
            'progress_percentage': float(case.progress_percentage),
            'is_overdue': case.is_overdue(),
            'deadline': case.deadline.isoformat() if case.deadline else None,
        })
        
    except exceptions.CaseNotFound:
        return JsonResponse({'error': 'Case not found'}, status=404)
    except Exception as e:
        logger.error(f"Error in api_case_status: {str(e)}")
        return JsonResponse({'error': 'Internal server error'}, status=500)


@login_required
@require_http_methods(["GET"])
def api_dashboard_stats(request):
    """
    API: Get dashboard statistics
    """
    try:
        statistics = services.get_case_statistics(uow=get_uow())
        return JsonResponse(statistics)
        
    except Exception as e:
        logger.error(f"Error in api_dashboard_stats: {str(e)}")
        return JsonResponse({'error': 'Internal server error'}, status=500)


# Helper functions
def _can_user_edit_case(user, case):
    """Check if user can edit the case"""
    # Implement your business logic here
    return user.is_staff or user.is_superuser


def _can_user_start_case(user, case):
    """Check if user can start the case"""
    from dental_lab.domain.model import CaseStatus
    return (
        _can_user_edit_case(user, case) and 
        case.status == CaseStatus.PENDING_ACCEPTANCE and
        len(case.items) > 0
    )


def _can_user_complete_case(user, case):
    """Check if user can complete the case"""
    from dental_lab.domain.model import CaseStatus
    return (
        _can_user_edit_case(user, case) and 
        case.status == CaseStatus.READY_TO_SHIP
    )


def _get_dentists_for_user(user):
    """Get dentists accessible to the user"""
    # Implement your business logic here
    # For now, return all dentists for staff users
    if user.is_staff:
        with get_uow() as unit_of_work:
            return unit_of_work.dentists.list_all_dentists()
    return []


def _get_available_items():
    """Get available items that can be added to cases"""
    # This would query the items catalog
    # For now, return empty list
    return []


def _get_user_role(user):
    """Get user role for dashboard customization"""
    if user.is_superuser:
        return 'admin'
    elif user.is_staff:
        return 'staff'
    else:
        return 'dentist'