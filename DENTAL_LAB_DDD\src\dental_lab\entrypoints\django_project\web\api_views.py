# DENTAL LAB DDD - REST API Views
# API endpoints using Django REST Framework

from rest_framework import status, generics, permissions
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.decorators import api_view, permission_classes
from django.http import Http404
import logging

from dental_lab.service_layer import services, uow
from dental_lab.domain import exceptions
from . import serializers

logger = logging.getLogger(__name__)


# Helper function
def get_uow():
    """Get Unit of Work instance"""
    return uow.DjangoUnitOfWork()


# Case API Views
class CaseListCreateAPIView(generics.ListCreateAPIView):
    """
    API View: List cases or create a new case
    GET /api/cases/ - List cases with filtering
    POST /api/cases/ - Create a new case
    """
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = serializers.CaseSerializer
    
    def get_queryset(self):
        """Get filtered list of cases"""
        try:
            # Get filter parameters
            status_filter = self.request.query_params.get('status')
            dentist_id = self.request.query_params.get('dentist_id')
            
            criteria = {}
            if status_filter:
                criteria['status'] = status_filter
            if dentist_id:
                criteria['dentist_id'] = int(dentist_id)
            
            with get_uow() as unit_of_work:
                if criteria:
                    return services.search_cases(criteria, unit_of_work)
                else:
                    return unit_of_work.cases.list_active_cases()
                    
        except Exception as e:
            logger.error(f"Error in CaseListCreateAPIView.get_queryset: {str(e)}")
            return []
    
    def perform_create(self, serializer):
        """Create a new case"""
        try:
            validated_data = serializer.validated_data
            
            case_number = services.create_new_case(
                dentist_id=validated_data['dentist_id'],
                patient_id=validated_data.get('patient_id'),
                deadline=validated_data.get('deadline'),
                priority=validated_data.get('priority', 2),
                notes=validated_data.get('notes', ''),
                special_requirements=validated_data.get('special_requirements', ''),
                items=validated_data.get('items', []),
                uow=get_uow()
            )
            
            # Get the created case for the response
            case = services.get_case_details(case_number, get_uow())
            serializer.instance = case
            
        except exceptions.DentistNotFound:
            raise serializers.ValidationError({'dentist_id': 'Dentist not found.'})
        except exceptions.PatientNotFound:
            raise serializers.ValidationError({'patient_id': 'Patient not found.'})
        except exceptions.PatientDentistMismatch:
            raise serializers.ValidationError({'patient_id': 'Patient does not belong to this dentist.'})


class CaseDetailAPIView(generics.RetrieveUpdateDestroyAPIView):
    """
    API View: Get, update, or delete a specific case
    GET /api/cases/{case_number}/ - Get case details
    PUT /api/cases/{case_number}/ - Update case
    DELETE /api/cases/{case_number}/ - Delete case
    """
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = serializers.CaseSerializer
    lookup_field = 'case_number'
    
    def get_object(self):
        """Get case by case number"""
        case_number = self.kwargs['case_number']
        try:
            return services.get_case_details(case_number, get_uow())
        except exceptions.CaseNotFound:
            raise Http404("Case not found")


class StartCaseAPIView(APIView):
    """
    API View: Start a case
    POST /api/cases/{case_number}/start/
    """
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request, case_number):
        try:
            services.start_case(
                case_number=case_number,
                started_by=request.user.id,
                uow=get_uow()
            )
            
            return Response(
                {'message': f'Case #{case_number} started successfully'},
                status=status.HTTP_200_OK
            )
            
        except exceptions.CaseNotFound:
            return Response(
                {'error': 'Case not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        except exceptions.CaseHasNoItems:
            return Response(
                {'error': 'Cannot start case without items'},
                status=status.HTTP_400_BAD_REQUEST
            )
        except exceptions.InvalidCaseStatus as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )


class CompleteCaseAPIView(APIView):
    """
    API View: Complete a case
    POST /api/cases/{case_number}/complete/
    """
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request, case_number):
        try:
            completion_notes = request.data.get('completion_notes', '')
            
            services.complete_case(
                case_number=case_number,
                completed_by=request.user.id,
                completion_notes=completion_notes,
                uow=get_uow()
            )
            
            return Response(
                {'message': f'Case #{case_number} completed successfully'},
                status=status.HTTP_200_OK
            )
            
        except exceptions.CaseNotFound:
            return Response(
                {'error': 'Case not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        except exceptions.InvalidCaseStatus as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
        except exceptions.CaseItemsNotCompleted:
            return Response(
                {'error': 'All case items must be completed first'},
                status=status.HTTP_400_BAD_REQUEST
            )


class AddItemToCaseAPIView(APIView):
    """
    API View: Add an item to a case
    POST /api/cases/{case_number}/items/
    """
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request, case_number):
        try:
            serializer = serializers.AddItemToCaseSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            
            data = serializer.validated_data
            
            services.add_item_to_case(
                case_number=case_number,
                item_id=data['item_id'],
                quantity=data['quantity'],
                unit_id=data['unit_id'],
                estimated_time=data.get('estimated_time'),
                special_instructions=data.get('special_instructions', ''),
                added_by=request.user.id,
                uow=get_uow()
            )
            
            return Response(
                {'message': 'Item added to case successfully'},
                status=status.HTTP_201_CREATED
            )
            
        except exceptions.CaseNotFound:
            return Response(
                {'error': 'Case not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        except exceptions.InvalidQuantity as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )


class OverdueCasesAPIView(generics.ListAPIView):
    """
    API View: List overdue cases
    GET /api/cases/overdue/
    """
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = serializers.CaseSerializer
    
    def get_queryset(self):
        try:
            return services.list_overdue_cases(get_uow())
        except Exception as e:
            logger.error(f"Error getting overdue cases: {str(e)}")
            return []


class CaseStatisticsAPIView(APIView):
    """
    API View: Get case statistics
    GET /api/cases/statistics/
    """
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request):
        try:
            statistics = services.get_case_statistics(uow=get_uow())
            return Response(statistics, status=status.HTTP_200_OK)
        except Exception as e:
            logger.error(f"Error getting case statistics: {str(e)}")
            return Response(
                {'error': 'Error retrieving statistics'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


# Patient API Views
class PatientListCreateAPIView(generics.ListCreateAPIView):
    """
    API View: List patients or create a new patient
    GET /api/patients/ - List patients
    POST /api/patients/ - Create a new patient
    """
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = serializers.PatientSerializer
    
    def get_queryset(self):
        try:
            dentist_id = self.request.query_params.get('dentist_id')
            if dentist_id:
                with get_uow() as unit_of_work:
                    return unit_of_work.patients.list_patients_by_dentist(int(dentist_id))
            return []
        except Exception as e:
            logger.error(f"Error getting patients: {str(e)}")
            return []
    
    def perform_create(self, serializer):
        try:
            validated_data = serializer.validated_data
            
            patient_id = services.register_patient(
                dentist_id=validated_data['dentist_id'],
                first_name=validated_data['first_name'],
                last_name=validated_data['last_name'],
                phone_number=validated_data.get('phone_number'),
                email=validated_data.get('email'),
                address=validated_data.get('address'),
                date_of_birth=validated_data.get('date_of_birth'),
                registered_by=self.request.user.id,
                uow=get_uow()
            )
            
            # Get the created patient for the response
            patient = services.get_patient_details(patient_id, get_uow())
            serializer.instance = patient
            
        except exceptions.DentistNotFound:
            raise serializers.ValidationError({'dentist_id': 'Dentist not found.'})
        except exceptions.InvalidPatientData as e:
            field = e.details.get('field', 'non_field_errors')
            raise serializers.ValidationError({field: str(e)})


class PatientDetailAPIView(generics.RetrieveUpdateDestroyAPIView):
    """
    API View: Get, update, or delete a specific patient
    GET /api/patients/{patient_id}/ - Get patient details
    PUT /api/patients/{patient_id}/ - Update patient
    DELETE /api/patients/{patient_id}/ - Delete patient
    """
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = serializers.PatientSerializer
    lookup_field = 'patient_id'
    
    def get_object(self):
        patient_id = self.kwargs['patient_id']
        try:
            return services.get_patient_details(patient_id, get_uow())
        except exceptions.PatientNotFound:
            raise Http404("Patient not found")


class PatientSearchAPIView(APIView):
    """
    API View: Search patients by name
    GET /api/patients/search/?q=name&dentist_id=123
    """
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request):
        query = request.query_params.get('q', '')
        dentist_id = request.query_params.get('dentist_id')
        
        if not query:
            return Response({'error': 'Query parameter q is required'}, 
                          status=status.HTTP_400_BAD_REQUEST)
        
        try:
            dentist_id = int(dentist_id) if dentist_id else None
            patients = services.search_patients(query, dentist_id, get_uow())
            serializer = serializers.PatientSerializer(patients, many=True)
            return Response(serializer.data, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"Error searching patients: {str(e)}")
            return Response(
                {'error': 'Error searching patients'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


# Dentist API Views  
class DentistListCreateAPIView(generics.ListCreateAPIView):
    """
    API View: List dentists or create a new dentist
    GET /api/dentists/ - List dentists
    POST /api/dentists/ - Create a new dentist
    """
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = serializers.DentistSerializer
    
    def get_queryset(self):
        try:
            with get_uow() as unit_of_work:
                return unit_of_work.dentists.list_all_dentists()
        except Exception as e:
            logger.error(f"Error getting dentists: {str(e)}")
            return []
    
    def perform_create(self, serializer):
        try:
            validated_data = serializer.validated_data
            
            dentist_id = services.register_dentist(
                first_name=validated_data['first_name'],
                last_name=validated_data['last_name'],
                clinic_name=validated_data['clinic_name'],
                phone_number=validated_data['phone_number'],
                email=validated_data.get('email'),
                address=validated_data.get('address'),
                user_id=validated_data.get('user_id'),
                registered_by=self.request.user.id,
                uow=get_uow()
            )
            
            # Get the created dentist for the response
            dentist = services.get_dentist_details(dentist_id, get_uow())
            serializer.instance = dentist
            
        except exceptions.InvalidDentistData as e:
            field = e.details.get('field', 'non_field_errors')
            raise serializers.ValidationError({field: str(e)})


class DentistDetailAPIView(generics.RetrieveUpdateDestroyAPIView):
    """
    API View: Get, update, or delete a specific dentist
    GET /api/dentists/{dentist_id}/ - Get dentist details
    PUT /api/dentists/{dentist_id}/ - Update dentist
    DELETE /api/dentists/{dentist_id}/ - Delete dentist
    """
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = serializers.DentistSerializer
    lookup_field = 'dentist_id'
    
    def get_object(self):
        dentist_id = self.kwargs['dentist_id']
        try:
            return services.get_dentist_details(dentist_id, get_uow())
        except exceptions.DentistNotFound:
            raise Http404("Dentist not found")


# Task API Views
class TaskListCreateAPIView(generics.ListCreateAPIView):
    """
    API View: List tasks or create a new task
    GET /api/tasks/ - List tasks
    POST /api/tasks/ - Create a new task
    """
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = serializers.TaskSerializer
    
    def get_queryset(self):
        try:
            case_number = self.request.query_params.get('case_number')
            assignee_id = self.request.query_params.get('assignee_id')
            
            with get_uow() as unit_of_work:
                if case_number:
                    return unit_of_work.tasks.list_tasks_by_case(int(case_number))
                elif assignee_id:
                    return unit_of_work.tasks.list_tasks_by_assignee(int(assignee_id))
                else:
                    return services.get_pending_tasks(uow=unit_of_work)
                    
        except Exception as e:
            logger.error(f"Error getting tasks: {str(e)}")
            return []


class TaskDetailAPIView(generics.RetrieveUpdateDestroyAPIView):
    """
    API View: Get, update, or delete a specific task
    GET /api/tasks/{task_id}/ - Get task details
    PUT /api/tasks/{task_id}/ - Update task
    DELETE /api/tasks/{task_id}/ - Delete task
    """
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = serializers.TaskSerializer
    lookup_field = 'task_id'
    
    def get_object(self):
        task_id = self.kwargs['task_id']
        try:
            with get_uow() as unit_of_work:
                task = unit_of_work.tasks.get_by_task_id(task_id)
                if not task:
                    raise Http404("Task not found")
                return task
        except Exception:
            raise Http404("Task not found")


class StartTaskAPIView(APIView):
    """
    API View: Start a task
    POST /api/tasks/{task_id}/start/
    """
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request, task_id):
        try:
            start_notes = request.data.get('start_notes', '')
            
            services.start_task(
                task_id=task_id,
                started_by=request.user.id,
                start_notes=start_notes,
                uow=get_uow()
            )
            
            return Response(
                {'message': f'Task #{task_id} started successfully'},
                status=status.HTTP_200_OK
            )
            
        except exceptions.TaskNotFound:
            return Response(
                {'error': 'Task not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        except exceptions.InvalidTaskStatus as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )


class CompleteTaskAPIView(APIView):
    """
    API View: Complete a task
    POST /api/tasks/{task_id}/complete/
    """
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request, task_id):
        try:
            completion_notes = request.data.get('completion_notes', '')
            quality_check_passed = request.data.get('quality_check_passed')
            
            services.complete_task(
                task_id=task_id,
                completed_by=request.user.id,
                completion_notes=completion_notes,
                quality_check_passed=quality_check_passed,
                uow=get_uow()
            )
            
            return Response(
                {'message': f'Task #{task_id} completed successfully'},
                status=status.HTTP_200_OK
            )
            
        except exceptions.TaskNotFound:
            return Response(
                {'error': 'Task not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        except exceptions.InvalidTaskStatus as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )


class PendingTasksAPIView(generics.ListAPIView):
    """
    API View: List pending tasks
    GET /api/tasks/pending/
    """
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = serializers.TaskSerializer
    
    def get_queryset(self):
        try:
            assignee_id = self.request.query_params.get('assignee_id')
            assignee_id = int(assignee_id) if assignee_id else None
            return services.get_pending_tasks(assignee_id, get_uow())
        except Exception as e:
            logger.error(f"Error getting pending tasks: {str(e)}")
            return []


class OverdueTasksAPIView(generics.ListAPIView):
    """
    API View: List overdue tasks
    GET /api/tasks/overdue/
    """
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = serializers.TaskSerializer
    
    def get_queryset(self):
        try:
            return services.get_overdue_tasks(get_uow())
        except Exception as e:
            logger.error(f"Error getting overdue tasks: {str(e)}")
            return []


# Dashboard and Analytics API Views
class DashboardStatsAPIView(APIView):
    """
    API View: Get dashboard statistics
    GET /api/dashboard/stats/
    """
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request):
        try:
            statistics = services.get_case_statistics(uow=get_uow())
            
            # Add additional dashboard data
            with get_uow() as unit_of_work:
                pending_tasks = services.get_pending_tasks(uow=unit_of_work)
                overdue_tasks = services.get_overdue_tasks(uow=unit_of_work)
            
            dashboard_data = {
                **statistics,
                'pending_tasks_count': len(pending_tasks),
                'overdue_tasks_count': len(overdue_tasks),
                'recent_activity': []  # Could be populated with recent events
            }
            
            return Response(dashboard_data, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"Error getting dashboard stats: {str(e)}")
            return Response(
                {'error': 'Error retrieving dashboard statistics'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class CaseAnalyticsAPIView(APIView):
    """
    API View: Get case analytics data
    GET /api/analytics/cases/
    """
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request):
        try:
            date_from = request.query_params.get('date_from')
            date_to = request.query_params.get('date_to')
            
            # Parse dates if provided
            if date_from:
                from datetime import datetime
                date_from = datetime.fromisoformat(date_from)
            if date_to:
                from datetime import datetime
                date_to = datetime.fromisoformat(date_to)
            
            # Get analytics data
            analytics = services.get_case_statistics(date_from, date_to, get_uow())
            
            return Response(analytics, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"Error getting case analytics: {str(e)}")
            return Response(
                {'error': 'Error retrieving analytics data'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )