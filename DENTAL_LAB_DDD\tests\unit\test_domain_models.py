# DENTAL LAB DDD - Unit Tests for Domain Models
# Fast tests for business logic - no external dependencies

import pytest
from datetime import datetime, timedelta
from decimal import Decimal

from dental_lab.domain import model, exceptions, events


class TestPatient:
    """Test the Patient entity"""
    
    def test_patient_creation(self):
        """Test basic patient creation"""
        name = model.PersonName("<PERSON>", "<PERSON><PERSON>")
        contact = model.ContactInfo(phone="************", email="<EMAIL>")
        
        patient = model.Patient(
            patient_id=1,
            name=name,
            dentist_id=100,
            contact=contact
        )
        
        assert patient.patient_id == 1
        assert patient.name.full_name == "<PERSON>"
        assert patient.dentist_id == 100
        assert patient.contact.phone == "************"
    
    def test_patient_age_calculation(self):
        """Test patient age calculation"""
        name = model.PersonName("<PERSON>", "<PERSON>")
        birth_date = datetime(1990, 5, 15)
        
        patient = model.Patient(
            patient_id=2,
            name=name,
            dentist_id=100,
            date_of_birth=birth_date
        )
        
        # Age calculation will depend on current date, but should be reasonable
        age = patient.age
        assert age is not None
        assert 30 <= age <= 40  # Reasonable range for someone born in 1990
    
    def test_patient_equality(self):
        """Test patient equality based on ID"""
        name = model.PersonName("Test", "Patient")
        
        patient1 = model.Patient(1, name, 100)
        patient2 = model.Patient(1, name, 100)
        patient3 = model.Patient(2, name, 100)
        
        assert patient1 == patient2
        assert patient1 != patient3
        assert hash(patient1) == hash(patient2)
        assert hash(patient1) != hash(patient3)


class TestDentist:
    """Test the Dentist entity"""
    
    def test_dentist_creation(self):
        """Test basic dentist creation"""
        name = model.PersonName("Dr. Sarah", "Wilson")
        contact = model.ContactInfo(
            phone="************",
            email="<EMAIL>",
            address="123 Main St"
        )
        
        dentist = model.Dentist(
            dentist_id=100,
            name=name,
            clinic_name="Smile Dental Clinic",
            contact=contact,
            user_id=500
        )
        
        assert dentist.dentist_id == 100
        assert dentist.name.full_name == "Dr. Sarah Wilson"
        assert dentist.clinic_name == "Smile Dental Clinic"
        assert dentist.user_id == 500


class TestCaseItem:
    """Test the CaseItem entity"""
    
    def test_case_item_creation(self):
        """Test basic case item creation"""
        item = model.CaseItem(
            case_number=1001,
            item_id=50,
            quantity=2,
            unit_id=1,
            estimated_time=timedelta(hours=3)
        )
        
        assert item.case_number == 1001
        assert item.item_id == 50
        assert item.quantity == 2
        assert item.status == model.TaskStatus.PENDING
        assert item.total_estimated_time == timedelta(hours=6)  # 3 hours * 2 quantity
    
    def test_case_item_invalid_quantity(self):
        """Test that invalid quantity raises error"""
        with pytest.raises(ValueError, match="Quantity must be positive"):
            model.CaseItem(
                case_number=1001,
                item_id=50,
                quantity=0,  # Invalid
                unit_id=1
            )
    
    def test_case_item_production_lifecycle(self):
        """Test case item production workflow"""
        item = model.CaseItem(
            case_number=1001,
            item_id=50,
            quantity=1,
            unit_id=1,
            estimated_time=timedelta(hours=2)
        )
        
        # Start production
        item.start_production()
        assert item.status == model.TaskStatus.IN_PROGRESS
        
        # Complete production
        actual_time = timedelta(hours=2, minutes=30)
        item.complete_production(actual_time)
        assert item.status == model.TaskStatus.COMPLETED
        assert item.actual_time == actual_time
        assert item.is_delayed == True  # Took longer than estimated
    
    def test_case_item_cannot_start_twice(self):
        """Test that item cannot be started twice"""
        item = model.CaseItem(1001, 50, 1, 1)
        item.start_production()
        
        with pytest.raises(ValueError, match="Cannot start production"):
            item.start_production()


class TestTask:
    """Test the Task entity"""
    
    def test_task_creation(self):
        """Test basic task creation"""
        task = model.Task(
            task_id=1,
            case_number=1001,
            title="Polish crown",
            estimated_duration=timedelta(hours=1)
        )
        
        assert task.task_id == 1
        assert task.case_number == 1001
        assert task.title == "Polish crown"
        assert task.status == model.TaskStatus.PENDING
        assert task.priority == model.Priority.NORMAL
        assert task.progress_percentage == Decimal('0')
    
    def test_task_lifecycle(self):
        """Test complete task lifecycle"""
        task = model.Task(
            task_id=1,
            case_number=1001,
            title="Test task",
            estimated_duration=timedelta(hours=1)
        )
        
        # Initially pending
        assert task.status == model.TaskStatus.PENDING
        assert task.can_start() == True
        
        # Start task
        start_time = datetime.now()
        task.start_task()
        assert task.status == model.TaskStatus.IN_PROGRESS
        assert task.actual_start_time is not None
        
        # Complete task
        task.complete_task()
        assert task.status == model.TaskStatus.COMPLETED
        assert task.actual_end_time is not None
        assert task.progress_percentage == Decimal('100')
        assert task.actual_duration is not None
    
    def test_task_pause_resume(self):
        """Test task pause and resume functionality"""
        task = model.Task(1, 1001, "Test task", timedelta(hours=1))
        
        task.start_task()
        assert task.status == model.TaskStatus.IN_PROGRESS
        
        # Pause task
        task.pause_task("Waiting for materials")
        assert task.status == model.TaskStatus.PAUSED
        assert task.blocking_issues == "Waiting for materials"
        
        # Try to complete paused task - should fail
        with pytest.raises(ValueError, match="Task must be in progress"):
            task.complete_task()
    
    def test_task_blocking(self):
        """Test task blocking functionality"""
        task = model.Task(1, 1001, "Test task", timedelta(hours=1))
        task.start_task()
        
        task.block_task("Equipment broken")
        assert task.status == model.TaskStatus.BLOCKED
        assert task.blocking_issues == "Equipment broken"


class TestCase:
    """Test the Case entity - the main aggregate root"""
    
    def test_case_creation(self):
        """Test basic case creation"""
        deadline = datetime.now() + timedelta(days=7)
        
        case = model.Case(
            case_number=1001,
            dentist_id=100,
            patient_id=1,
            deadline=deadline
        )
        
        assert case.case_number == 1001
        assert case.dentist_id == 100
        assert case.patient_id == 1
        assert case.status == model.CaseStatus.PENDING_ACCEPTANCE
        assert case.deadline == deadline
        assert case.items == []
        assert case.tasks == []
        assert len(case.events) == 0
    
    def test_case_add_item(self):
        """Test adding items to a case"""
        case = model.Case(1001, 100, 1)
        
        case.add_item(
            item_id=50,
            quantity=2,
            unit_id=1,
            estimated_time=timedelta(hours=3)
        )
        
        assert len(case.items) == 1
        assert case.items[0].item_id == 50
        assert case.items[0].quantity == 2
        assert len(case.events) == 1
        assert isinstance(case.events[0], events.ItemAddedToCase)
    
    def test_case_add_duplicate_item_updates_quantity(self):
        """Test that adding duplicate item updates quantity"""
        case = model.Case(1001, 100, 1)
        
        case.add_item(50, 2, 1)
        case.add_item(50, 3, 1)  # Same item
        
        assert len(case.items) == 1
        assert case.items[0].quantity == 5  # 2 + 3
    
    def test_case_cannot_add_items_when_completed(self):
        """Test that completed cases cannot have items added"""
        case = model.Case(1001, 100, 1)
        case.status = model.CaseStatus.COMPLETED
        
        with pytest.raises(ValueError, match="Cannot add items to completed case"):
            case.add_item(50, 1, 1)
    
    def test_case_select_teeth(self):
        """Test selecting teeth for a case"""
        case = model.Case(1001, 100, 1)
        
        case.select_teeth([1, 2, 3, 16], "A2")
        
        assert case.selected_teeth == {1, 2, 3, 16}
        assert case.teeth_color == "A2"
    
    def test_case_invalid_tooth_numbers(self):
        """Test that invalid tooth numbers are rejected"""
        case = model.Case(1001, 100, 1)
        
        with pytest.raises(ValueError, match="Invalid tooth number"):
            case.select_teeth([0, 33, 50])  # Invalid tooth numbers
    
    def test_case_deadline_setting(self):
        """Test setting case deadline with validation"""
        case = model.Case(1001, 100, 1)
        future_date = datetime.now() + timedelta(days=5)
        
        case.set_deadline(future_date)
        assert case.deadline == future_date
    
    def test_case_cannot_set_past_deadline(self):
        """Test that past deadlines are rejected"""
        case = model.Case(1001, 100, 1)
        past_date = datetime.now() - timedelta(days=1)
        
        with pytest.raises(ValueError, match="Deadline cannot be in the past"):
            case.set_deadline(past_date)
    
    def test_case_start_workflow(self):
        """Test starting a case"""
        case = model.Case(1001, 100, 1)
        case.add_item(50, 1, 1)  # Must have items to start
        
        case.start_case()
        
        assert case.status == model.CaseStatus.IN_PROGRESS
        assert case.received_date_time is not None
        assert len(case.events) == 2  # ItemAdded + CaseStarted
        assert isinstance(case.events[1], events.CaseStarted)
    
    def test_case_cannot_start_without_items(self):
        """Test that case cannot start without items"""
        case = model.Case(1001, 100, 1)
        
        with pytest.raises(ValueError, match="Cannot start case without items"):
            case.start_case()
    
    def test_case_completion_workflow(self):
        """Test complete case workflow"""
        case = model.Case(1001, 100, 1)
        case.add_item(50, 1, 1)
        case.start_case()
        
        # Complete the item first
        case.items[0].complete_production(timedelta(hours=2))
        
        # Move to ready to ship
        case.status = model.CaseStatus.READY_TO_SHIP
        
        # Now can complete
        case.complete_case()
        
        assert case.status == model.CaseStatus.COMPLETED
        assert case.actual_completion is not None
        assert case.progress_percentage == Decimal('100')
        assert any(isinstance(event, events.CaseCompleted) for event in case.events)
    
    def test_case_cannot_complete_with_incomplete_items(self):
        """Test that case cannot complete with incomplete items"""
        case = model.Case(1001, 100, 1)
        case.add_item(50, 1, 1)
        case.status = model.CaseStatus.READY_TO_SHIP
        # Item is still pending
        
        with pytest.raises(ValueError, match="All items must be completed"):
            case.complete_case()
    
    def test_case_is_overdue(self):
        """Test overdue detection"""
        past_deadline = datetime.now() - timedelta(days=1)
        future_deadline = datetime.now() + timedelta(days=1)
        
        # Overdue case
        overdue_case = model.Case(1001, 100, 1, deadline=past_deadline)
        assert overdue_case.is_overdue() == True
        
        # Not overdue case
        future_case = model.Case(1002, 100, 1, deadline=future_deadline)
        assert future_case.is_overdue() == False
        
        # Completed case is never overdue
        completed_case = model.Case(1003, 100, 1, deadline=past_deadline)
        completed_case.status = model.CaseStatus.COMPLETED
        assert completed_case.is_overdue() == False
    
    def test_case_revision_workflow(self):
        """Test case revision workflow"""
        case = model.Case(1001, 100, 1)
        case.status = model.CaseStatus.QUALITY_CHECK
        
        case.require_revision("Crown needs adjustment")
        
        assert case.status == model.CaseStatus.REVISION_NEEDED
        assert case.revision_count == 1
        assert case.delay_reason == "Crown needs adjustment"
    
    def test_case_cancellation(self):
        """Test case cancellation"""
        case = model.Case(1001, 100, 1)
        case.status = model.CaseStatus.IN_PROGRESS
        
        case.cancel_case("Patient changed mind")
        
        assert case.status == model.CaseStatus.CANCELLED
        assert case.delay_reason == "Patient changed mind"
    
    def test_case_cannot_cancel_completed(self):
        """Test that completed cases cannot be cancelled"""
        case = model.Case(1001, 100, 1)
        case.status = model.CaseStatus.COMPLETED
        
        with pytest.raises(ValueError, match="Cannot cancel completed case"):
            case.cancel_case("Test reason")


class TestValueObjects:
    """Test value objects"""
    
    def test_money_creation(self):
        """Test Money value object creation"""
        money = model.Money(Decimal('100.50'), "EUR")
        
        assert money.amount == Decimal('100.50')
        assert money.currency == "EUR"
    
    def test_money_addition(self):
        """Test Money addition"""
        money1 = model.Money(Decimal('100.00'), "EUR")
        money2 = model.Money(Decimal('50.50'), "EUR")
        
        result = money1 + money2
        
        assert result.amount == Decimal('150.50')
        assert result.currency == "EUR"
    
    def test_money_different_currency_addition_fails(self):
        """Test that adding different currencies fails"""
        eur_money = model.Money(Decimal('100.00'), "EUR")
        usd_money = model.Money(Decimal('100.00'), "USD")
        
        with pytest.raises(ValueError, match="Cannot add USD to EUR"):
            eur_money + usd_money
    
    def test_money_negative_amount_fails(self):
        """Test that negative amounts are rejected"""
        with pytest.raises(ValueError, match="Money amount cannot be negative"):
            model.Money(Decimal('-10.00'), "EUR")
    
    def test_person_name_creation(self):
        """Test PersonName value object"""
        name = model.PersonName("John", "Doe")
        
        assert name.first_name == "John"
        assert name.last_name == "Doe"
        assert name.full_name == "John Doe"
    
    def test_person_name_empty_first_name_fails(self):
        """Test that empty first name is rejected"""
        with pytest.raises(ValueError, match="First name cannot be empty"):
            model.PersonName("", "Doe")
    
    def test_contact_info_validation(self):
        """Test ContactInfo validation"""
        # Valid email
        valid_contact = model.ContactInfo(email="<EMAIL>")
        assert valid_contact.email == "<EMAIL>"
        
        # Invalid email
        with pytest.raises(ValueError, match="Invalid email format"):
            model.ContactInfo(email="invalid-email")