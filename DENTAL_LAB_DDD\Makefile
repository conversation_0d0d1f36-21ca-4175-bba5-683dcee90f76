# DENTAL LAB DDD - Makefile
# Development automation for dental lab management system

.PHONY: help install test lint format clean docker-build docker-run

# Default target
help:
	@echo "Dental Lab DDD - Available commands:"
	@echo ""
	@echo "Development:"
	@echo "  install     Install dependencies"
	@echo "  install-dev Install development dependencies"
	@echo "  test        Run all tests"
	@echo "  test-unit   Run unit tests only"
	@echo "  test-integration  Run integration tests only"
	@echo "  test-e2e    Run end-to-end tests"
	@echo "  coverage    Run tests with coverage report"
	@echo ""
	@echo "Code Quality:"
	@echo "  lint        Run linting (flake8, mypy)"
	@echo "  format      Format code (black, isort)"
	@echo "  check-format Check if code is properly formatted"
	@echo ""
	@echo "Django:"
	@echo "  migrate     Run Django migrations"
	@echo "  makemigrations Create new migrations"
	@echo "  runserver   Start development server"
	@echo "  shell       Open Django shell"
	@echo "  createsuperuser Create Django superuser"
	@echo ""
	@echo "Docker:"
	@echo "  docker-build Build Docker image"
	@echo "  docker-run   Run application in Docker"
	@echo "  docker-test  Run tests in Docker"
	@echo ""
	@echo "Cleanup:"
	@echo "  clean       Clean up temporary files"
	@echo "  clean-db    Remove database file (development only)"

# Installation
install:
	pip install -r requirements.txt

install-dev:
	pip install -r requirements.txt
	pip install -e .[dev]

# Testing
test:
	pytest

test-unit:
	pytest tests/unit/ -v

test-integration:
	pytest tests/integration/ -v

test-e2e:
	pytest tests/e2e/ -v

coverage:
	pytest --cov=src/dental_lab --cov-report=html --cov-report=term-missing

# Code Quality
lint:
	flake8 src/dental_lab tests
	mypy src/dental_lab

format:
	black src/dental_lab tests
	isort src/dental_lab tests

check-format:
	black --check src/dental_lab tests
	isort --check-only src/dental_lab tests

# Django Commands
migrate:
	cd src/dental_lab/entrypoints/django_project && python manage.py migrate

makemigrations:
	cd src/dental_lab/entrypoints/django_project && python manage.py makemigrations

runserver:
	cd src/dental_lab/entrypoints/django_project && python manage.py runserver

shell:
	cd src/dental_lab/entrypoints/django_project && python manage.py shell

createsuperuser:
	cd src/dental_lab/entrypoints/django_project && python manage.py createsuperuser

# Docker
docker-build:
	docker build -t dental-lab-ddd .

docker-run:
	docker-compose up

docker-test:
	docker-compose -f docker-compose.test.yml up --build --abort-on-container-exit

# Cleanup
clean:
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	rm -rf .coverage htmlcov/ .pytest_cache/ dist/ build/

clean-db:
	rm -f src/dental_lab/entrypoints/django_project/db.sqlite3

# Development workflow
dev-setup: install-dev migrate
	@echo "Development environment setup complete!"
	@echo "Run 'make runserver' to start the development server"

# CI/CD helpers
ci-test: lint test coverage
	@echo "All CI checks passed!"

# Quick development cycle
dev: format lint test
	@echo "Development cycle complete!"