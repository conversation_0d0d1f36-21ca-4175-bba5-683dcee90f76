# DENTAL LAB DDD - Web URLs
# URL configuration for the web interface

from django.urls import path
from . import views

app_name = 'web'

urlpatterns = [
    # Dashboard
    path('', views.dashboard_view, name='dashboard'),
    path('dashboard/', views.dashboard_view, name='dashboard_alt'),
    
    # Case Management
    path('cases/', views.case_list_view, name='case_list'),
    path('cases/create/', views.create_case_view, name='create_case'),
    path('cases/<int:case_number>/', views.case_detail_view, name='case_detail'),
    path('cases/<int:case_number>/start/', views.start_case_view, name='start_case'),
    path('cases/<int:case_number>/complete/', views.complete_case_view, name='complete_case'),
    path('cases/<int:case_number>/add-item/', views.add_item_to_case_view, name='add_item_to_case'),
    path('cases/overdue/', views.overdue_cases_view, name='overdue_cases'),
    
    # Patient Management
    path('patients/', views.patient_list_view, name='patient_list'),
    path('patients/create/', views.create_patient_view, name='create_patient'),
    
    # API endpoints for AJAX
    path('api/cases/<int:case_number>/status/', views.api_case_status, name='api_case_status'),
    path('api/dashboard/stats/', views.api_dashboard_stats, name='api_dashboard_stats'),
]