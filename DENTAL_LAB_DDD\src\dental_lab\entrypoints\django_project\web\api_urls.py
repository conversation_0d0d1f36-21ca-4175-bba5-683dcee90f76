# DENTAL LAB DDD - API URLs
# REST API endpoints configuration

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import api_views

# Create router for ViewSets
router = DefaultRouter()
# router.register(r'cases', api_views.CaseViewSet)
# router.register(r'patients', api_views.PatientViewSet)
# router.register(r'dentists', api_views.DentistViewSet)

app_name = 'api'

urlpatterns = [
    # Include router URLs
    path('', include(router.urls)),
    
    # Authentication
    path('auth/', include('rest_framework.urls')),
    
    # Custom API endpoints
    path('cases/', api_views.CaseListCreateAPIView.as_view(), name='case-list-create'),
    path('cases/<int:case_number>/', api_views.CaseDetailAPIView.as_view(), name='case-detail'),
    path('cases/<int:case_number>/start/', api_views.StartCaseAPIView.as_view(), name='case-start'),
    path('cases/<int:case_number>/complete/', api_views.CompleteCaseAPIView.as_view(), name='case-complete'),
    path('cases/<int:case_number>/items/', api_views.AddItemToCaseAPIView.as_view(), name='case-add-item'),
    path('cases/overdue/', api_views.OverdueCasesAPIView.as_view(), name='overdue-cases'),
    path('cases/statistics/', api_views.CaseStatisticsAPIView.as_view(), name='case-statistics'),
    
    # Patient API endpoints
    path('patients/', api_views.PatientListCreateAPIView.as_view(), name='patient-list-create'),
    path('patients/<int:patient_id>/', api_views.PatientDetailAPIView.as_view(), name='patient-detail'),
    path('patients/search/', api_views.PatientSearchAPIView.as_view(), name='patient-search'),
    
    # Dentist API endpoints  
    path('dentists/', api_views.DentistListCreateAPIView.as_view(), name='dentist-list-create'),
    path('dentists/<int:dentist_id>/', api_views.DentistDetailAPIView.as_view(), name='dentist-detail'),
    
    # Task API endpoints
    path('tasks/', api_views.TaskListCreateAPIView.as_view(), name='task-list-create'),
    path('tasks/<int:task_id>/', api_views.TaskDetailAPIView.as_view(), name='task-detail'),
    path('tasks/<int:task_id>/start/', api_views.StartTaskAPIView.as_view(), name='task-start'),
    path('tasks/<int:task_id>/complete/', api_views.CompleteTaskAPIView.as_view(), name='task-complete'),
    path('tasks/pending/', api_views.PendingTasksAPIView.as_view(), name='pending-tasks'),
    path('tasks/overdue/', api_views.OverdueTasksAPIView.as_view(), name='overdue-tasks'),
    
    # Dashboard and Analytics
    path('dashboard/stats/', api_views.DashboardStatsAPIView.as_view(), name='dashboard-stats'),
    path('analytics/cases/', api_views.CaseAnalyticsAPIView.as_view(), name='case-analytics'),
]